#!/usr/bin/env python3
"""
Developer Tools for ServiceBot
This script provides tools for developers to manage activation codes and monitor bot usage.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'helpers'))

from helpers.data import init_db, add_activation_code, get_developer_logs, log_to_developers
import secrets
import string
import sqlite3

def generate_activation_code(length=16):
    """Generate a secure activation code"""
    alphabet = string.ascii_uppercase + string.digits
    return ''.join(secrets.choice(alphabet) for _ in range(length))

def create_activation_codes():
    """Create new activation codes"""
    print("🔑 Create Activation Codes")
    print("-" * 30)
    
    try:
        count = int(input("How many activation codes to generate? "))
        if count <= 0:
            print("❌ Invalid count")
            return
    except ValueError:
        print("❌ Invalid number")
        return
    
    print(f"\nGenerating {count} activation codes...")
    
    created_codes = []
    for i in range(count):
        code = generate_activation_code()
        if add_activation_code(code):
            created_codes.append(code)
            print(f"✅ Created: {code}")
        else:
            print(f"❌ Failed to create code (might already exist)")
    
    print(f"\n✅ Successfully created {len(created_codes)} activation codes")
    
    # Save codes to file
    if created_codes:
        with open("activation_codes.txt", "a") as f:
            f.write(f"\n# Generated on {__import__('datetime').datetime.now()}\n")
            for code in created_codes:
                f.write(f"{code}\n")
        print("📄 Codes saved to activation_codes.txt")

def view_activation_status():
    """View all activation codes and their status"""
    print("📊 Activation Status")
    print("-" * 30)
    
    conn = sqlite3.connect('bot.db')
    cursor = conn.cursor()
    cursor.execute('''
        SELECT activation_code, is_activated, activated_at, bot_instance_id, customer_info
        FROM bot_activation
        ORDER BY is_activated DESC, activated_at DESC
    ''')
    results = cursor.fetchall()
    conn.close()
    
    if not results:
        print("No activation codes found.")
        return
    
    print(f"{'Code':<20} {'Status':<12} {'Activated At':<20} {'Instance ID':<30}")
    print("-" * 90)
    
    for code, activated, activated_at, instance_id, customer_info in results:
        status = "✅ ACTIVE" if activated else "⏳ UNUSED"
        activated_at_str = activated_at[:19] if activated_at else "N/A"
        instance_id_str = instance_id[:25] + "..." if instance_id and len(instance_id) > 25 else instance_id or "N/A"
        
        print(f"{code:<20} {status:<12} {activated_at_str:<20} {instance_id_str:<30}")
        
        if customer_info:
            print(f"{'':>20} Customer: {customer_info}")

def view_developer_logs():
    """View recent developer logs"""
    print("📋 Developer Logs")
    print("-" * 30)
    
    try:
        limit = int(input("Number of recent logs to show (default 20): ") or "20")
    except ValueError:
        limit = 20
    
    logs = get_developer_logs(limit)
    
    if not logs:
        print("No logs found.")
        return
    
    for log in logs:
        print(f"\n[{log['timestamp']}] {log['log_type']}")
        print(f"Message: {log['message']}")
        if log['user_id']:
            print(f"User ID: {log['user_id']}")
        if log['order_id']:
            print(f"Order ID: {log['order_id']}")
        if log['additional_data']:
            print(f"Additional: {log['additional_data']}")
        print("-" * 50)

def deactivate_bot():
    """Deactivate a bot (emergency use)"""
    print("🚨 Deactivate Bot")
    print("-" * 30)
    print("⚠️  WARNING: This will deactivate a bot instance!")
    
    activation_code = input("Enter activation code to deactivate: ").strip()
    if not activation_code:
        print("❌ No activation code provided")
        return
    
    confirm = input(f"Are you sure you want to deactivate bot with code '{activation_code}'? (yes/no): ")
    if confirm.lower() != 'yes':
        print("❌ Deactivation cancelled")
        return
    
    conn = sqlite3.connect('bot.db')
    cursor = conn.cursor()
    cursor.execute('''
        UPDATE bot_activation 
        SET is_activated = 0, bot_instance_id = NULL, customer_info = NULL
        WHERE activation_code = ? AND is_activated = 1
    ''', (activation_code,))
    
    if cursor.rowcount > 0:
        conn.commit()
        print("✅ Bot deactivated successfully")
        log_to_developers("BOT_DEACTIVATED", f"Bot deactivated by developer", additional_data=f"Code: {activation_code}")
    else:
        print("❌ No active bot found with that activation code")
    
    conn.close()

def main():
    print("🛠️  ServiceBot Developer Tools")
    print("=" * 40)
    
    # Initialize database
    init_db()
    
    while True:
        print("\nSelect an option:")
        print("1. Create activation codes")
        print("2. View activation status")
        print("3. View developer logs")
        print("4. Deactivate bot (emergency)")
        print("5. Exit")
        
        choice = input("\nEnter your choice (1-5): ").strip()
        
        if choice == "1":
            create_activation_codes()
        elif choice == "2":
            view_activation_status()
        elif choice == "3":
            view_developer_logs()
        elif choice == "4":
            deactivate_bot()
        elif choice == "5":
            print("👋 Goodbye!")
            break
        else:
            print("❌ Invalid choice. Please try again.")

if __name__ == "__main__":
    main()
