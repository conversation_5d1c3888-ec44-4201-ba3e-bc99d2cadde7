"""
Remote Control System for ServiceBot
This module allows the admin bot to remotely control customer bot instances.
"""

import requests
import json
import threading
import time
import logging
from datetime import datetime, timedelta
from helpers.data import log_to_developers, get_activation_status

# Remote control configuration
REMOTE_CONTROL_CHECK_INTERVAL = 30  # Check for commands every 30 seconds
REMOTE_CONTROL_URL = "https://your-api-endpoint.com/bot-control/commands"
REMOTE_CONTROL_ENABLED = True

# Bot control state
bot_control_state = {
    'active': True,
    'locked_functions': [],
    'maintenance_mode': False,
    'last_check': None,
    'admin_message': None
}

control_lock = threading.RLock()

class RemoteControlHandler:
    """Handles remote control commands from admin bot"""
    
    def __init__(self):
        self.running = False
        self.thread = None
        
    def start(self):
        """Start the remote control handler"""
        if not REMOTE_CONTROL_ENABLED:
            return
            
        self.running = True
        self.thread = threading.Thread(target=self._control_loop, daemon=True)
        self.thread.start()
        logging.info("Remote control handler started")
        
    def stop(self):
        """Stop the remote control handler"""
        self.running = False
        if self.thread:
            self.thread.join(timeout=5)
        logging.info("Remote control handler stopped")
        
    def _control_loop(self):
        """Main control loop that checks for admin commands"""
        while self.running:
            try:
                self._check_for_commands()
                time.sleep(REMOTE_CONTROL_CHECK_INTERVAL)
            except Exception as e:
                logging.error(f"Error in remote control loop: {e}")
                time.sleep(60)  # Wait longer on error
                
    def _check_for_commands(self):
        """Check for remote control commands"""
        try:
            # Get bot identification
            activation_status = get_activation_status()
            if not activation_status['activated']:
                return
                
            bot_id = activation_status['activation_code']
            
            # Request commands from admin API
            response = requests.get(
                f"{REMOTE_CONTROL_URL}/{bot_id}",
                timeout=10,
                headers={'User-Agent': 'ServiceBot/1.0'}
            )
            
            if response.status_code == 200:
                commands = response.json()
                self._process_commands(commands)
                
            with control_lock:
                bot_control_state['last_check'] = datetime.now()
                
        except requests.RequestException as e:
            logging.warning(f"Failed to check remote commands: {e}")
        except Exception as e:
            logging.error(f"Error checking remote commands: {e}")
            
    def _process_commands(self, commands):
        """Process received commands"""
        for command in commands.get('commands', []):
            try:
                self._execute_command(command)
            except Exception as e:
                logging.error(f"Error executing command {command}: {e}")
                
    def _execute_command(self, command):
        """Execute a single command"""
        cmd_type = command.get('type')
        cmd_data = command.get('data', {})
        
        with control_lock:
            if cmd_type == 'deactivate':
                self._handle_deactivate(cmd_data)
            elif cmd_type == 'lock_function':
                self._handle_lock_function(cmd_data)
            elif cmd_type == 'unlock_function':
                self._handle_unlock_function(cmd_data)
            elif cmd_type == 'maintenance_mode':
                self._handle_maintenance_mode(cmd_data)
            elif cmd_type == 'admin_message':
                self._handle_admin_message(cmd_data)
            elif cmd_type == 'restart':
                self._handle_restart(cmd_data)
            else:
                logging.warning(f"Unknown command type: {cmd_type}")
                
        # Log command execution
        log_to_developers(
            "REMOTE_COMMAND", 
            f"Executed remote command: {cmd_type}", 
            additional_data=json.dumps(cmd_data)
        )
        
    def _handle_deactivate(self, data):
        """Handle bot deactivation command"""
        reason = data.get('reason', 'Admin deactivation')
        
        bot_control_state['active'] = False
        bot_control_state['admin_message'] = f"🚨 Bot deactivated: {reason}"
        
        logging.critical(f"Bot deactivated by admin: {reason}")
        
    def _handle_lock_function(self, data):
        """Handle function lock command"""
        function_name = data.get('function')
        if function_name:
            if function_name not in bot_control_state['locked_functions']:
                bot_control_state['locked_functions'].append(function_name)
            logging.warning(f"Function locked by admin: {function_name}")
            
    def _handle_unlock_function(self, data):
        """Handle function unlock command"""
        function_name = data.get('function')
        if function_name in bot_control_state['locked_functions']:
            bot_control_state['locked_functions'].remove(function_name)
            logging.info(f"Function unlocked by admin: {function_name}")
            
    def _handle_maintenance_mode(self, data):
        """Handle maintenance mode command"""
        enabled = data.get('enabled', False)
        message = data.get('message', 'Bot is under maintenance')
        
        bot_control_state['maintenance_mode'] = enabled
        if enabled:
            bot_control_state['admin_message'] = f"🔧 {message}"
        else:
            bot_control_state['admin_message'] = None
            
        logging.info(f"Maintenance mode {'enabled' if enabled else 'disabled'}")
        
    def _handle_admin_message(self, data):
        """Handle admin message command"""
        message = data.get('message')
        duration = data.get('duration', 3600)  # 1 hour default
        
        bot_control_state['admin_message'] = message
        
        # Schedule message removal
        def remove_message():
            time.sleep(duration)
            with control_lock:
                if bot_control_state['admin_message'] == message:
                    bot_control_state['admin_message'] = None
                    
        threading.Thread(target=remove_message, daemon=True).start()
        
    def _handle_restart(self, data):
        """Handle restart command"""
        delay = data.get('delay', 10)
        
        def restart_bot():
            time.sleep(delay)
            logging.critical("Bot restart requested by admin")
            # This would trigger a restart mechanism
            import os
            os._exit(1)  # Force exit, supervisor should restart
            
        threading.Thread(target=restart_bot, daemon=True).start()

# Global remote control handler
remote_control = RemoteControlHandler()

def is_bot_active():
    """Check if bot is active (not deactivated by admin)"""
    with control_lock:
        return bot_control_state['active']

def is_function_locked(function_name):
    """Check if a specific function is locked by admin"""
    with control_lock:
        return function_name in bot_control_state['locked_functions']

def is_maintenance_mode():
    """Check if bot is in maintenance mode"""
    with control_lock:
        return bot_control_state['maintenance_mode']

def get_admin_message():
    """Get current admin message if any"""
    with control_lock:
        return bot_control_state['admin_message']

def get_control_status():
    """Get current control status"""
    with control_lock:
        return {
            'active': bot_control_state['active'],
            'locked_functions': bot_control_state['locked_functions'].copy(),
            'maintenance_mode': bot_control_state['maintenance_mode'],
            'admin_message': bot_control_state['admin_message'],
            'last_check': bot_control_state['last_check']
        }

def function_access_control(function_name):
    """Decorator to control access to bot functions"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            # Check if bot is active
            if not is_bot_active():
                admin_msg = get_admin_message()
                if admin_msg:
                    return admin_msg
                return "🚨 Bot is currently deactivated by administrators."
            
            # Check if function is locked
            if is_function_locked(function_name):
                return f"🔒 Function '{function_name}' is currently locked by administrators."
            
            # Check maintenance mode
            if is_maintenance_mode():
                admin_msg = get_admin_message()
                if admin_msg:
                    return admin_msg
                return "🔧 Bot is currently under maintenance. Please try again later."
            
            # Execute function normally
            return func(*args, **kwargs)
        return wrapper
    return decorator

def start_remote_control():
    """Start the remote control system"""
    remote_control.start()

def stop_remote_control():
    """Stop the remote control system"""
    remote_control.stop()

# Auto-start remote control when module is imported
if REMOTE_CONTROL_ENABLED:
    start_remote_control()
