from helpers.buttons import *

# Define the start command handler
def start(update: Update, context: CallbackContext) -> None:
    delete_chat_commands(update, context)
    if is_admin(update.effective_user.id):
        keyboard = [
            [InlineKeyboardButton("Services", callback_data='services')],
            [Inline<PERSON>eyboardButton("FAQ", callback_data='faq')],
            [InlineKeyboardButton("ToS", callback_data='tos')],
            [InlineKeyboardButton("My orders", callback_data='my_orders')],
            [InlineKeyboardButton("Admin options", callback_data='admin-options')],
        ]
    else:
        keyboard = [
            [InlineKeyboardButton("Services", callback_data='services')],
            [InlineKeyboardButton("FAQ", callback_data='faq')],
            [InlineKeyboardButton("ToS", callback_data='tos')],
            [InlineKeyboardButton("My orders", callback_data='my_orders')],  # Add "My orders" button
        ]
    message = update.message
    context.user_data['start_message_id'] = message.message_id
    reply_markup = InlineKeyboardMarkup(keyboard)
    update.message.reply_text(WELCOME_MESSAGE, reply_markup=reply_markup)

def myOrders(update: Update, context: CallbackContext) -> None:
    delete_chat_commands(update, context)
    user_id = update.effective_user.id
    confirmed_orders = get_confirmed_orders(user_id)
    if confirmed_orders:
        keyboard = [[InlineKeyboardButton(order['order_id'], callback_data=f'order_{order["order_id"]}')] for order in confirmed_orders]
        keyboard.append([InlineKeyboardButton(BACK_BUTTON_TEXT, callback_data='back')])  # Add back button
        reply_markup = InlineKeyboardMarkup(keyboard)
        update.message.reply_text(MYORDER_MESSAGE_TEXT, reply_markup=reply_markup)
    else:
        update.message.reply_text(NO_ORDERS_MESSAGE_TEXT, parse_mode='Markdown')

def faq(update: Update, context: CallbackContext) -> None:
    delete_chat_commands(update, context)
    update.message.reply_text(FAQ_TEXT, parse_mode='Markdown')

def tos(update: Update, context: CallbackContext) -> None:
    delete_chat_commands(update, context)
    update.message.reply_text(TOS_TEXT, parse_mode='Markdown')

def status(update: Update, context: CallbackContext) -> None:
    delete_chat_commands(update, context)
    user_id = str(update.effective_user.id)
    if context.args:
        order_id = " ".join(context.args)
        if user_has_order(user_id, order_id):
            status_text, buttons_data = get_status_info(order_id)
            needed_amount = get_order_amount_needed(order_id)
            needed_currency = get_order_currency(order_id)
            message = f"📦 Order: {order_id}\n\n📝 Order details:\n- Store name: {get_store_name(order_id)}\n{get_order_details(order_id)}\n\n💡 Status:\n{status_text.format(amount=needed_amount, currency=needed_currency)}"

            keyboard = []
            for btn in buttons_data:
                raw_callback = btn["callback_data"]
                if "{order_id}" in raw_callback:
                    custom_callback = raw_callback.format(order_id=order_id)
                else:
                    custom_callback = raw_callback
                btn_text = btn["text"]
                keyboard.append([InlineKeyboardButton(btn_text, callback_data=custom_callback)])
            reply_markup = InlineKeyboardMarkup(keyboard)
            update.message.reply_text(text=message, parse_mode='Markdown', reply_markup=reply_markup)
        else:
            update.message.reply_text("Order ID not found", parse_mode='Markdown')
    else:
        update.message.reply_text("No order ID provided, please provide an order ID.")

def order_info(update: Update, context: CallbackContext) -> None:
    delete_chat_commands(update, context)
    if not is_admin(update.effective_user.id):
        update.message.reply_text("❌ Admin access required")
        return

    if len(context.args) != 2:
        update.message.reply_text("❌ Usage: /order_info <order_id> <user_id>")
        return
    
    order_id, user_id = context.args
    message = get_full_order_details(order_id, user_id)
    update.message.reply_text(message, parse_mode='Markdown')

def setup_commands(dispatcher: Dispatcher):
    dispatcher.add_handler(CommandHandler("start", start))
    dispatcher.add_handler(CommandHandler("myorders", myOrders))
    dispatcher.add_handler(CommandHandler("faq", faq))
    dispatcher.add_handler(CommandHandler("tos", tos))
    dispatcher.add_handler(CommandHandler("status", status))
    dispatcher.add_handler(CommandHandler("order_info", order_info))