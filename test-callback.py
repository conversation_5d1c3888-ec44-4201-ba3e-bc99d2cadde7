import requests
import curses

# Note: If you are on Windows, you need to install the `windows-curses` package:
# pip install windows-curses

# Define the URL and parameters
url = 'http://localhost:5000/callback'
params = {
    'userid': '5553293151',
    'orderid': 'TestOrderID'
}

# Define the JSON payloads
data_1 = {
    'uuid':'dbfcb40e-5a6b-4305-9fa2-b0fbda6e3ff2', # uuid of cryptapi to identify the order
    'address_in': '**********************************', # crypto address where customer sent funds
    'address_out':'**********************************', # crypto address where cryptapi will forward the funds
    'txid_in': 'a2174ffd39289100709f2a07b129cdbba69df2e22e5be1830221dab1fd4e332c', # txid of the transaction to cryptapi's address
    'confirmations': 3, # number of confirmations so far
    'value_coin': 0.5, # amount sent by customer before fees
    'value_coin_convert': {"USD": "500", "EUR": "450", "GBP": "400", "CAD": "475"}, # amount sent by customer before fees in FIAT
    'coin': 'BTC', # crypto currency used
    'price': 10000.0, # price of the currency at the time of the transaction
    'pending': 1, # waiting for confirmations
}

data_2 = {
    'uuid':'dbfcb40e-5a6b-4305-9fa2-b0fbda6e3ff2', # uuid of cryptapi to identify the order
    'address_in': '**********************************', # crypto address where customer sent funds
    'address_out':'**********************************', # crypto address where cryptapi will forward the funds
    'txid_in': 'a2174ffd39289100709f2a07b129cdbba69df2e22e5be1830221dab1fd4e332c', # txid of the transaction to cryptapi's address
    'txid_out': 'a2174ffd39289100709f2a07b129cdbba69df2e22e5be1830221dab1fd4e332c', # txid of the transaction to our address
    'confirmations': 3, # number of confirmations so far
    'value_coin': 0.5, # amount sent by customer before fees
    'value_coin_convert': {"USD": "500", "EUR": "450", "GBP": "400", "CAD": "475"}, # amount sent by customer before fees in FIAT
    'value_forwarded_coin': 0.4, # amount forwarded to our address
    'value_forwarded_coin_convert': {"USD": "480", "EUR": "430", "GBP": "380", "CAD": "425"}, # amount forwarded to our address in FIAT
    'fee_coin': 0.1, # fee paid to cryptapi
    'coin': 'BTC', # crypto currency used
    'price': 10000.0,
    'pending': 0,
}

def send_request(data, orderid):
    params['orderid'] = orderid
    response = requests.post(url, params=params, json=data)
    print(f'Status Code: {response.status_code}')
    print(f'Response Text: {response.text}')

def main(stdscr):
    curses.curs_set(0)
    stdscr.nodelay(1)
    stdscr.timeout(100)
    
    options = ["Test pending payment", "Test confirmed payment", "Change Order ID", "Exit"]
    current_row = 0
    orderid = params['orderid']

    while True:
        stdscr.clear()
        h, w = stdscr.getmaxyx()
        
        # Display the current orderid at the top
        banner = f"Current Order ID: {orderid}"
        stdscr.addstr(1, w // 2 - len(banner) // 2, banner)
        
        # Adjust menu starting row to account for banner
        menu_start_row = 3  # Start the menu display from row 3

        for idx, row in enumerate(options):
            x = w//2 - len(row)//2
            y = menu_start_row + idx
            if idx == current_row:
                stdscr.attron(curses.A_REVERSE)
                stdscr.addstr(y, x, row)
                stdscr.attroff(curses.A_REVERSE)
            else:
                stdscr.addstr(y, x, row)

        key = stdscr.getch()

        if key == curses.KEY_UP and current_row > 0:
            current_row -= 1
        elif key == curses.KEY_DOWN and current_row < len(options) - 1:
            current_row += 1
        elif key == curses.KEY_ENTER or key in [10, 13]:
            if current_row == 0:
                send_request(data_1, orderid)
            elif current_row == 1:
                send_request(data_2, orderid)
            elif current_row == 2:
                curses.echo()
                stdscr.nodelay(0)    # Switch to blocking mode
                stdscr.timeout(-1)    # Disable timeout
                curses.flushinp()     # Clear input buffer

                # Clear the line and display the prompt
                stdscr.addstr(h//2 + len(options)//2 + 1, 0, " " * w)
                prompt = "Enter new Order ID: "
                stdscr.addstr(h//2 + len(options)//2 + 1, w//2 - len(prompt)//2, prompt)
                stdscr.refresh()

                # Create a new window for input
                input_win = curses.newwin(1, w - (w//2 - len(prompt)//2 + len(prompt) + 2),
                                          h//2 + len(options)//2 + 1, w//2 - len(prompt)//2 + len(prompt))
                input_win.nodelay(0)   # Make input blocking
                curses.curs_set(1)     # Show the cursor
                orderid = ''
                while True:
                    ch = input_win.getch()
                    if ch in [10, 13]:  # Enter key
                        break
                    elif ch in [8, 127, curses.KEY_BACKSPACE]:  # Backspace key
                        if len(orderid) > 0:
                            orderid = orderid[:-1]
                            input_win.delch(0, len(orderid))
                    elif 32 <= ch <= 126:  # Printable characters
                        orderid += chr(ch)
                        input_win.addstr(0, len(orderid)-1, chr(ch))
                    input_win.refresh()

                curses.curs_set(0)     # Hide the cursor
                curses.noecho()
                stdscr.nodelay(1)      # Switch back to non-blocking mode
                stdscr.timeout(100)    # Restore timeout
            elif current_row == 3:
                break

        stdscr.refresh()

curses.wrapper(main)