"""
================================================================================
                        SERVICEBOT CONFIGURATION FILE
================================================================================

🤖 Welcome to ServiceBot Configuration!

This is the ONLY file you need to modify to customize your bot. All other files
are encrypted and should not be changed.

📋 CONFIGURATION CHECKLIST:
[ ] 1. Set your Telegram Bot Token
[ ] 2. Configure your cryptocurrency wallet addresses
[ ] 3. Set up your Telegram group chat IDs
[ ] 4. Configure your callback URL
[ ] 5. Customize store settings (optional)
[ ] 6. Customize bot messages (optional)

⚠️  IMPORTANT NOTES:
- Developer fees (4% total) are automatically included in all transactions
- You will receive 96% of all payments, developers receive 4% for support
- Never share this file or your activation code with anyone
- Make backups of this file after configuration

🆘 NEED HELP?
- Email: <EMAIL>
- Telegram: @ServiceBotSupport
- Documentation: See README.md

================================================================================
"""

# ============================================================================
# 1. TELEGRAM BOT TOKEN
# ============================================================================
# Get your bot token from @BotFather on Telegram
# 1. Message @BotFather
# 2. Send /newbot
# 3. Follow the instructions
# 4. Copy the token and paste it below

BOT_TOKEN = "YOUR_BOT_TOKEN_HERE"  # ⚠️ REQUIRED: Replace with your actual bot token

# ============================================================================
# 2. CRYPTOCURRENCY WALLET ADDRESSES
# ============================================================================
# Enter your cryptocurrency wallet addresses below
# These are where you will receive payments (minus 4% developer fee)
#
# 💡 HOW TO GET WALLET ADDRESSES:
# - Bitcoin (BTC): Use Electrum, Exodus, or any Bitcoin wallet
# - Ethereum (ETH): Use MetaMask, MyEtherWallet, or any Ethereum wallet
# - Litecoin (LTC): Use Litecoin Core, Exodus, or any Litecoin wallet
# - Solana (SOL): Use Phantom, Solflare, or any Solana wallet
#
# ⚠️ IMPORTANT NOTES:
# - Developer fees (4% total) are automatically deducted from all payments
# - You receive 96% of each payment, developers receive 4% for support
# - Double-check addresses - incorrect addresses will result in lost funds
# - Leave any address empty ("") to disable that cryptocurrency

crypto_addresses = {
    "btc": "",      # 🟡 Your Bitcoin address (e.g., ******************************************)
    "eth": "",      # 🟡 Your Ethereum address (e.g., ******************************************)
    "ltc": "",      # 🟡 Your Litecoin address (e.g., LTC2xYz789ABCdef123456789012345678)
    "sol/sol": "",  # 🟡 Your Solana address (e.g., SOL2xYz789ABCdef123456789012345678901234567)
}

# 💡 EXAMPLE (DO NOT USE THESE ADDRESSES):
# crypto_addresses = {
#     "btc": "******************************************",
#     "eth": "******************************************",
#     "ltc": "LTC2xYz789ABCdef123456789012345678",
#     "sol/sol": "SOL2xYz789ABCdef123456789012345678901234567",
# }

# ============================================================================
# 3. TELEGRAM GROUP CHAT IDs
# ============================================================================
# Set up your Telegram groups for order management
#
# 📋 HOW TO GET GROUP CHAT IDs:
# 1. Create your Telegram groups
# 2. Add @getmyid_bot to each group
# 3. The bot will send you the group ID (format: -*************)
# 4. Copy the IDs below (include the minus sign!)
#
# 📝 GROUP TYPES:
# - REQUEST GROUPS: Where new orders are sent for approval/decline
# - FINAL ORDER GROUPS: Where confirmed orders are sent for fulfillment
#
# 💡 TIP: You can use the same group ID for multiple stores

REQUEST_GROUP_CHAT_IDS = {
    "Nike": "",      # 🟡 Group where Nike orders are sent for approval
    "Adidas": "",    # 🟡 Group where Adidas orders are sent for approval
    "Zara": "",      # 🟡 Group where Zara orders are sent for approval
    "H&M": "",       # 🟡 Group where H&M orders are sent for approval
    "Gucci": "",     # 🟡 Group where Gucci orders are sent for approval
}

FINAL_ORDER_GROUP_CHAT_IDS = {
    "Nike": "",      # 🟡 Group where confirmed Nike orders are sent
    "Adidas": "",    # 🟡 Group where confirmed Adidas orders are sent
    "Zara": "",      # 🟡 Group where confirmed Zara orders are sent
    "H&M": "",       # 🟡 Group where confirmed H&M orders are sent
    "Gucci": "",     # 🟡 Group where confirmed Gucci orders are sent
}

# 💡 EXAMPLE (DO NOT USE THESE IDs):
# REQUEST_GROUP_CHAT_IDS = {
#     "Nike": "-*************",
#     "Adidas": "-*************",
#     # ... etc
# }

# Stores configuration
stores = {
    "Nike": {
        "store_name": "Nike",
        "fee": "15% of item value. Minimum fee: $30.",
        "domains": ".com, .eu, .asia",
        "order_limits": "3 items per order",
        "required_info": "- Product SKU\n- Size (US/EU)\n- Color variant\n- Style number\n- Preferred shipping method",
        "extra_info": "Ensure your Nike membership account is active before submitting."
    },
    "Adidas": {
        "store_name": "Adidas",
        "fee": "12% of item value. Minimum fee: $25.",
        "domains": ".com, .de, .uk",
        "order_limits": "5 items per order",
        "required_info": "- Product code\n- Size (US/EU)\n- Color preference\n- Special edition details\n- Delivery timeframe",
        "extra_info": "Provide valid phone number for delivery updates."
    },
    "Zara": {
        "store_name": "Zara", 
        "fee": "18% of item value. Minimum fee: $20.",
        "domains": ".com, .es, .fr",
        "order_limits": "4 items per order",
        "required_info": "- Collection season\n- Item category\n- Size (Zara sizing)\n- Fabric preference\n- Packaging requirements",
        "extra_info": "Specify if you need international sizing conversion."
    },
    "H&M": {
        "store_name": "H&M",
        "fee": "10% of item value. Minimum fee: $15.",
        "domains": ".com, .se, .de",
        "order_limits": "6 items per order",
        "required_info": "- Product line\n- Size (H&M sizing)\n- Color options\n- Sustainability preferences\n- Gift wrapping needs",
        "extra_info": "Mention any allergy concerns for fabric types."
    },
    "Gucci": {
        "store_name": "Gucci",
        "fee": "20% of item value. Minimum fee: $100.",
        "domains": ".com, .it, .cn",
        "order_limits": "2 luxury items per order",
        "required_info": "- Authentication code\n- Limited edition number\n- Material specifications\n- Preferred boutique location\n- Personalization details",
        "extra_info": "VIP members must provide membership ID."
    }
}


# You can change the following text to customize store templates
# Just KEEP OFF THINGS LIKE {store_name} and other placeholders

# You can change titles like: "💰 Fee Structure:".

STORE_TEMPLATE = """👕 {store_name}

💰 Fee Structure:
{fee}

🌍 Available Regions:
{domains}

📏 Order Limitations:
{order_limits}

📋 Required Details
{required_info}

💡 Important Notes
{extra_info}

Select confirm to submit your fashion order."""

# KEEP OFF THE FOLLOWING CODE:

def get_store_text(store_name):
    store = stores.get(store_name)
    if store:
        return STORE_TEMPLATE.format(**store)
    return "Store not found."


# Here we are defining the text for each store using the template
# So make sure that when you add a new store, you add it here too.

STORE_1_TEXT = get_store_text("Nike")
STORE_2_TEXT = get_store_text("Adidas")
STORE_3_TEXT = get_store_text("Zara")
STORE_4_TEXT = get_store_text("H&M")
STORE_5_TEXT = get_store_text("Gucci")

## Your callback URL for the bot
CALLBACK_BASE_URL = "http://xx.xxx.xxx.xxx:5000/callback"


# Some texts you may want to change:

WELCOME_MESSAGE = "Welcome to our bot! Choose an option below:"
CONTACT_ADMIN = "@username"

FAQ_TEXT = "Here are the frequently asked questions:\nQ: What is this bot for?\nA: This bot is for testing purposes only."


TOS_TEXT = "By using this bot, you agree to the following terms:\n- You will not spam the bot.\n- You will not share inappropriate content."


SERVICES_TEXT = "Hi there!\n\nPlease choose a service below:"