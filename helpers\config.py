'''
Allright, let's set up the configuration for the bot.
This is where we will define the bot token, admin IDs, and other necessary configurations.
We will also set up the database connection and other necessary configurations.

So let's get started.

1. Fill in your crypto addresses
You can do this by replacing the "fillinbtcaddress" with your actual crypto address.

2. Fill in your group chat IDs
You can do this by replacing the "fillin_request_chat_id" with your actual group chat ID.

How to find your group chat ID?
- Create a group and add @@getmyid_bot to the group.
- This bot will send you a message with the group ID.
- The chatID will be in the format of -1001234567890.
- Replace the "fillin_request_chat_id" with the actual group ID.

Some info:
The request chat ID is the chat ID where the bot will send the request for orders.
In there you will be able to accept and decline orders.

In the final order chat ID, the bot will send the final order details after the order is accepted.

3. Fill in your store details, more info below.

4. MAKE SURE YOU Fill in your bot token

5. Callback URL should be set to your server's URL where the bot is hosted. (e.g., http://yourdomain.com/callback)

If you want some info about this callbac url, you can read the following:
A callback URL is a URL that the bot will use to send updates to your server. So when a user sends a payment, the bot will send a request to this URL with the payment details.
If this URL is not set, the bot will not be able to send updates to your server. Therefore, it is important to set this URL to your server's URL where the bot is hosted.

Some more info about the paymments and crypto addresses:
The bot will generate a specific payment address for each order. This will not look like your actual crypto address, but rather a unique address for each order.
This is because of the API we are using to handle the payments. It will generate a unique address for each order, and the bot will send the payment details to this address.
Ofcourse, you will get the payment to your actual crypto address, but the API will first accept it and then send it to your actual crypto address.

6. At the end of this file there are some more texts, you can change them if you want. (e.g., WELCOME_MESSAGE, ...)

Yup, that's it!
'''

BOT_TOKEN = "8156432455:AAHbaJrmdPKS-IYDt6rXsOs7JtLct_LSBYs"  # Replace with your bot token

crypto_addresses = {
    "btc": "fillinbtcaddress",
    "eth": "fillinethaddress",
    "ltc": "fillinltcaddress",
    "sol/sol": "fillinsoladdress",
}

REQUEST_GROUP_CHAT_IDS = {
    "Nike": "-4674258499",
    "Adidas": "-4674258499",
    "Zara": "-4674258499",
    "H&M": "-4674258499",
    "Gucci": "-4674258499"
}

FINAL_ORDER_GROUP_CHAT_IDS = {
    "Nike": "-**********",
    "Adidas": "-**********",
    "Zara": "-**********",
    "H&M": "-**********",
    "Gucci": "-**********"
}

# Stores configuration
stores = {
    "Nike": {
        "store_name": "Nike",
        "fee": "15% of item value. Minimum fee: $30.",
        "domains": ".com, .eu, .asia",
        "order_limits": "3 items per order",
        "required_info": "- Product SKU\n- Size (US/EU)\n- Color variant\n- Style number\n- Preferred shipping method",
        "extra_info": "Ensure your Nike membership account is active before submitting."
    },
    "Adidas": {
        "store_name": "Adidas",
        "fee": "12% of item value. Minimum fee: $25.",
        "domains": ".com, .de, .uk",
        "order_limits": "5 items per order",
        "required_info": "- Product code\n- Size (US/EU)\n- Color preference\n- Special edition details\n- Delivery timeframe",
        "extra_info": "Provide valid phone number for delivery updates."
    },
    "Zara": {
        "store_name": "Zara", 
        "fee": "18% of item value. Minimum fee: $20.",
        "domains": ".com, .es, .fr",
        "order_limits": "4 items per order",
        "required_info": "- Collection season\n- Item category\n- Size (Zara sizing)\n- Fabric preference\n- Packaging requirements",
        "extra_info": "Specify if you need international sizing conversion."
    },
    "H&M": {
        "store_name": "H&M",
        "fee": "10% of item value. Minimum fee: $15.",
        "domains": ".com, .se, .de",
        "order_limits": "6 items per order",
        "required_info": "- Product line\n- Size (H&M sizing)\n- Color options\n- Sustainability preferences\n- Gift wrapping needs",
        "extra_info": "Mention any allergy concerns for fabric types."
    },
    "Gucci": {
        "store_name": "Gucci",
        "fee": "20% of item value. Minimum fee: $100.",
        "domains": ".com, .it, .cn",
        "order_limits": "2 luxury items per order",
        "required_info": "- Authentication code\n- Limited edition number\n- Material specifications\n- Preferred boutique location\n- Personalization details",
        "extra_info": "VIP members must provide membership ID."
    }
}


# You can change the following text to customize store templates
# Just KEEP OFF THINGS LIKE {store_name} and other placeholders

# You can change titles like: "💰 Fee Structure:".

STORE_TEMPLATE = """👕 {store_name}

💰 Fee Structure:
{fee}

🌍 Available Regions:
{domains}

📏 Order Limitations:
{order_limits}

📋 Required Details
{required_info}

💡 Important Notes
{extra_info}

Select confirm to submit your fashion order."""

# KEEP OFF THE FOLLOWING CODE:

def get_store_text(store_name):
    store = stores.get(store_name)
    if store:
        return STORE_TEMPLATE.format(**store)
    return "Store not found."


# Here we are defining the text for each store using the template
# So make sure that when you add a new store, you add it here too.

STORE_1_TEXT = get_store_text("Nike")
STORE_2_TEXT = get_store_text("Adidas")
STORE_3_TEXT = get_store_text("Zara")
STORE_4_TEXT = get_store_text("H&M")
STORE_5_TEXT = get_store_text("Gucci")

## Your callback URL for the bot
CALLBACK_BASE_URL = "http://xx.xxx.xxx.xxx:5000/callback"


# Some texts you may want to change:

WELCOME_MESSAGE = "Welcome to our bot! Choose an option below:"
CONTACT_ADMIN = "@username"

FAQ_TEXT = "Here are the frequently asked questions:\nQ: What is this bot for?\nA: This bot is for testing purposes only."


TOS_TEXT = "By using this bot, you agree to the following terms:\n- You will not spam the bot.\n- You will not share inappropriate content."


SERVICES_TEXT = "Hi there!\n\nPlease choose a service below:"