#!/usr/bin/env python3
"""
Admin Control Bot for ServiceBot
This bot allows developers to remotely control and monitor customer bot instances.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'helpers'))

from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import Updater, CommandHandler, CallbackQueryHandler, CallbackContext, MessageHandler, Filters
import logging
import requests
import json
from datetime import datetime
from helpers.data import init_db, get_developer_logs, add_activation_code
import sqlite3

# Admin bot configuration
ADMIN_BOT_TOKEN = "YOUR_ADMIN_BOT_TOKEN_HERE"  # Set this to your admin bot token
AUTHORIZED_ADMIN_IDS = [7789224316]  # Add authorized admin user IDs here

# Remote control API endpoint (would be hosted by developers)
REMOTE_CONTROL_API = "https://your-api-endpoint.com/bot-control"

logging.basicConfig(format='%(asctime)s - %(name)s - %(levelname)s - %(message)s', level=logging.INFO)

def is_authorized_admin(user_id):
    """Check if user is an authorized admin"""
    return user_id in AUTHORIZED_ADMIN_IDS

def admin_start(update: Update, context: CallbackContext):
    """Admin bot start command"""
    user_id = update.effective_user.id
    
    if not is_authorized_admin(user_id):
        update.message.reply_text("❌ Unauthorized access. This bot is for developers only.")
        return
    
    keyboard = [
        [InlineKeyboardButton("📊 View Bot Status", callback_data='view_status')],
        [InlineKeyboardButton("🔑 Manage Activation Codes", callback_data='manage_codes')],
        [InlineKeyboardButton("📋 View Logs", callback_data='view_logs')],
        [InlineKeyboardButton("🚨 Emergency Controls", callback_data='emergency_controls')],
        [InlineKeyboardButton("📈 Analytics", callback_data='analytics')]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)
    
    update.message.reply_text(
        "🛠️ **ServiceBot Admin Control Panel**\n\n"
        "Welcome to the admin control system. Select an option below:",
        reply_markup=reply_markup,
        parse_mode='Markdown'
    )

def admin_callback_handler(update: Update, context: CallbackContext):
    """Handle admin bot callbacks"""
    query = update.callback_query
    query.answer()
    
    user_id = query.from_user.id
    if not is_authorized_admin(user_id):
        query.edit_message_text("❌ Unauthorized access.")
        return
    
    data = query.data
    
    if data == 'view_status':
        view_bot_status(query, context)
    elif data == 'manage_codes':
        manage_activation_codes(query, context)
    elif data == 'view_logs':
        view_developer_logs_admin(query, context)
    elif data == 'emergency_controls':
        show_emergency_controls(query, context)
    elif data == 'analytics':
        show_analytics(query, context)
    elif data.startswith('deactivate_'):
        activation_code = data.split('_', 1)[1]
        deactivate_bot_instance(query, context, activation_code)
    elif data.startswith('generate_codes_'):
        count = int(data.split('_')[2])
        generate_activation_codes(query, context, count)

def view_bot_status(query, context):
    """View status of all bot instances"""
    conn = sqlite3.connect('bot.db')
    cursor = conn.cursor()
    
    # Get activation status
    cursor.execute('''
        SELECT activation_code, is_activated, activated_at, bot_instance_id, customer_info
        FROM bot_activation
        ORDER BY is_activated DESC, activated_at DESC
        LIMIT 20
    ''')
    results = cursor.fetchall()
    conn.close()
    
    if not results:
        query.edit_message_text("📊 **Bot Status**\n\nNo bot instances found.")
        return
    
    message = "📊 **Bot Status Report**\n\n"
    active_count = 0
    inactive_count = 0
    
    for code, activated, activated_at, instance_id, customer_info in results:
        status = "🟢 ACTIVE" if activated else "⚪ INACTIVE"
        if activated:
            active_count += 1
        else:
            inactive_count += 1
        
        message += f"**Code:** `{code[:8]}...`\n"
        message += f"**Status:** {status}\n"
        
        if activated and activated_at:
            message += f"**Activated:** {activated_at[:19]}\n"
        
        if instance_id:
            message += f"**Instance:** `{instance_id[:30]}...`\n"
        
        if customer_info:
            message += f"**Customer:** {customer_info[:50]}...\n"
        
        message += "─" * 30 + "\n"
    
    message += f"\n📈 **Summary:**\n"
    message += f"🟢 Active: {active_count}\n"
    message += f"⚪ Inactive: {inactive_count}\n"
    message += f"📊 Total: {len(results)}"
    
    keyboard = [[InlineKeyboardButton("🔄 Refresh", callback_data='view_status')]]
    reply_markup = InlineKeyboardMarkup(keyboard)
    
    query.edit_message_text(message, reply_markup=reply_markup, parse_mode='Markdown')

def manage_activation_codes(query, context):
    """Manage activation codes"""
    keyboard = [
        [InlineKeyboardButton("➕ Generate 1 Code", callback_data='generate_codes_1')],
        [InlineKeyboardButton("➕ Generate 5 Codes", callback_data='generate_codes_5')],
        [InlineKeyboardButton("➕ Generate 10 Codes", callback_data='generate_codes_10')],
        [InlineKeyboardButton("📋 View All Codes", callback_data='view_all_codes')],
        [InlineKeyboardButton("🔙 Back", callback_data='back_to_main')]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)
    
    query.edit_message_text(
        "🔑 **Activation Code Management**\n\n"
        "Select an option to manage activation codes:",
        reply_markup=reply_markup,
        parse_mode='Markdown'
    )

def generate_activation_codes(query, context, count):
    """Generate new activation codes"""
    import secrets
    import string
    
    generated_codes = []
    
    for _ in range(count):
        code = ''.join(secrets.choice(string.ascii_uppercase + string.digits) for _ in range(16))
        if add_activation_code(code):
            generated_codes.append(code)
    
    if generated_codes:
        message = f"✅ **Generated {len(generated_codes)} Activation Codes:**\n\n"
        for code in generated_codes:
            message += f"`{code}`\n"
        
        # Save to file
        with open("new_activation_codes.txt", "a") as f:
            f.write(f"\n# Generated on {datetime.now()}\n")
            for code in generated_codes:
                f.write(f"{code}\n")
        
        message += f"\n📄 Codes saved to `new_activation_codes.txt`"
    else:
        message = "❌ Failed to generate activation codes."
    
    keyboard = [[InlineKeyboardButton("🔙 Back", callback_data='manage_codes')]]
    reply_markup = InlineKeyboardMarkup(keyboard)
    
    query.edit_message_text(message, reply_markup=reply_markup, parse_mode='Markdown')

def view_developer_logs_admin(query, context):
    """View recent developer logs"""
    logs = get_developer_logs(10)
    
    if not logs:
        message = "📋 **Developer Logs**\n\nNo logs found."
    else:
        message = "📋 **Recent Developer Logs**\n\n"
        
        for log in logs[:10]:  # Show last 10 logs
            message += f"**[{log['timestamp']}]** {log['log_type']}\n"
            message += f"📝 {log['message'][:100]}...\n"
            
            if log['user_id']:
                message += f"👤 User: `{log['user_id']}`\n"
            
            if log['order_id']:
                message += f"📦 Order: `{log['order_id']}`\n"
            
            message += "─" * 25 + "\n"
    
    keyboard = [
        [InlineKeyboardButton("🔄 Refresh", callback_data='view_logs')],
        [InlineKeyboardButton("🔙 Back", callback_data='back_to_main')]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)
    
    query.edit_message_text(message, reply_markup=reply_markup, parse_mode='Markdown')

def show_emergency_controls(query, context):
    """Show emergency control options"""
    keyboard = [
        [InlineKeyboardButton("🚨 Deactivate All Bots", callback_data='emergency_deactivate_all')],
        [InlineKeyboardButton("📢 Send Global Message", callback_data='send_global_message')],
        [InlineKeyboardButton("🔒 Lock Bot Functions", callback_data='lock_functions')],
        [InlineKeyboardButton("🔙 Back", callback_data='back_to_main')]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)
    
    query.edit_message_text(
        "🚨 **Emergency Controls**\n\n"
        "⚠️ **WARNING:** These actions affect all bot instances!\n\n"
        "Use these controls only in emergency situations:",
        reply_markup=reply_markup,
        parse_mode='Markdown'
    )

def show_analytics(query, context):
    """Show bot analytics"""
    conn = sqlite3.connect('bot.db')
    cursor = conn.cursor()
    
    # Get various statistics
    cursor.execute('SELECT COUNT(*) FROM bot_activation WHERE is_activated = 1')
    active_bots = cursor.fetchone()[0]
    
    cursor.execute('SELECT COUNT(*) FROM bot_activation')
    total_codes = cursor.fetchone()[0]
    
    cursor.execute('SELECT COUNT(*) FROM orders')
    total_orders = cursor.fetchone()[0]
    
    cursor.execute('SELECT COUNT(*) FROM developer_logs WHERE log_type = "PAYMENT_CONFIRMED"')
    confirmed_payments = cursor.fetchone()[0]
    
    conn.close()
    
    message = f"📈 **Analytics Dashboard**\n\n"
    message += f"🤖 **Active Bots:** {active_bots}\n"
    message += f"🔑 **Total Codes:** {total_codes}\n"
    message += f"📦 **Total Orders:** {total_orders}\n"
    message += f"💰 **Confirmed Payments:** {confirmed_payments}\n"
    message += f"📊 **Activation Rate:** {(active_bots/total_codes*100):.1f}%\n" if total_codes > 0 else "📊 **Activation Rate:** 0%\n"
    
    keyboard = [
        [InlineKeyboardButton("🔄 Refresh", callback_data='analytics')],
        [InlineKeyboardButton("🔙 Back", callback_data='back_to_main')]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)
    
    query.edit_message_text(message, reply_markup=reply_markup, parse_mode='Markdown')

def main():
    """Main function for admin bot"""
    if not ADMIN_BOT_TOKEN or ADMIN_BOT_TOKEN == "YOUR_ADMIN_BOT_TOKEN_HERE":
        print("❌ Please set ADMIN_BOT_TOKEN in admin_bot.py")
        return
    
    init_db()
    
    updater = Updater(ADMIN_BOT_TOKEN)
    dispatcher = updater.dispatcher
    
    # Add handlers
    dispatcher.add_handler(CommandHandler("start", admin_start))
    dispatcher.add_handler(CallbackQueryHandler(admin_callback_handler))
    
    print("🛠️ Admin bot started successfully!")
    print("Send /start to the admin bot to access the control panel.")
    
    updater.start_polling()
    updater.idle()

if __name__ == '__main__':
    main()
