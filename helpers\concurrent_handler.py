"""
Concurrent Handler for ServiceBot
This module provides thread-safe operations and concurrent user handling to support up to 500 simultaneous users.
"""

import threading
import queue
import time
import logging
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from functools import wraps
import sqlite3
from contextlib import contextmanager

# Thread pool for handling concurrent operations
MAX_WORKERS = 50  # Adjust based on server capacity
executor = ThreadPoolExecutor(max_workers=MAX_WORKERS)

# Database connection pool
DB_POOL_SIZE = 20
db_pool = queue.Queue(maxsize=DB_POOL_SIZE)

# Initialize database connection pool
def init_db_pool():
    """Initialize database connection pool"""
    for _ in range(DB_POOL_SIZE):
        conn = sqlite3.connect('bot.db', check_same_thread=False)
        conn.execute('PRAGMA journal_mode=WAL')  # Enable WAL mode for better concurrency
        db_pool.put(conn)

@contextmanager
def get_db_connection():
    """Get a database connection from the pool"""
    conn = None
    try:
        conn = db_pool.get(timeout=5)  # Wait up to 5 seconds for a connection
        yield conn
    except queue.Empty:
        # If no connection available, create a temporary one
        conn = sqlite3.connect('bot.db', check_same_thread=False)
        conn.execute('PRAGMA journal_mode=WAL')
        yield conn
    finally:
        if conn:
            try:
                db_pool.put_nowait(conn)
            except queue.Full:
                # Pool is full, close the connection
                conn.close()

def thread_safe_db_operation(func):
    """Decorator to make database operations thread-safe"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        with get_db_connection() as conn:
            return func(conn, *args, **kwargs)
    return wrapper

class ConcurrentUserHandler:
    """Handles concurrent user operations"""
    
    def __init__(self):
        self.active_users = {}
        self.user_locks = {}
        self.global_lock = threading.RLock()
        
    def get_user_lock(self, user_id):
        """Get or create a lock for a specific user"""
        with self.global_lock:
            if user_id not in self.user_locks:
                self.user_locks[user_id] = threading.RLock()
            return self.user_locks[user_id]
    
    def execute_user_operation(self, user_id, operation, *args, **kwargs):
        """Execute an operation for a specific user in a thread-safe manner"""
        user_lock = self.get_user_lock(user_id)
        
        def wrapped_operation():
            with user_lock:
                try:
                    return operation(*args, **kwargs)
                except Exception as e:
                    logging.error(f"Error in user operation for {user_id}: {e}")
                    raise
        
        # Submit to thread pool
        future = executor.submit(wrapped_operation)
        return future
    
    def execute_concurrent_operations(self, operations):
        """Execute multiple operations concurrently"""
        futures = []
        for operation, args, kwargs in operations:
            future = executor.submit(operation, *args, **kwargs)
            futures.append(future)
        
        results = []
        for future in as_completed(futures, timeout=30):
            try:
                result = future.result()
                results.append(result)
            except Exception as e:
                logging.error(f"Concurrent operation failed: {e}")
                results.append(None)
        
        return results

# Global instance
user_handler = ConcurrentUserHandler()

def async_database_operation(operation_func):
    """Decorator for async database operations"""
    @wraps(operation_func)
    def wrapper(*args, **kwargs):
        def db_operation():
            try:
                return operation_func(*args, **kwargs)
            except Exception as e:
                logging.error(f"Database operation failed: {e}")
                raise
        
        future = executor.submit(db_operation)
        return future
    return wrapper

class NonBlockingCache:
    """Non-blocking cache for frequently accessed data"""
    
    def __init__(self, max_size=1000, ttl=300):  # 5 minutes TTL
        self.cache = {}
        self.timestamps = {}
        self.max_size = max_size
        self.ttl = ttl
        self.lock = threading.RLock()
    
    def get(self, key):
        """Get value from cache"""
        with self.lock:
            if key in self.cache:
                # Check if expired
                if time.time() - self.timestamps[key] < self.ttl:
                    return self.cache[key]
                else:
                    # Remove expired entry
                    del self.cache[key]
                    del self.timestamps[key]
            return None
    
    def set(self, key, value):
        """Set value in cache"""
        with self.lock:
            # Remove oldest entries if cache is full
            if len(self.cache) >= self.max_size:
                oldest_key = min(self.timestamps.keys(), key=lambda k: self.timestamps[k])
                del self.cache[oldest_key]
                del self.timestamps[oldest_key]
            
            self.cache[key] = value
            self.timestamps[key] = time.time()
    
    def clear(self):
        """Clear cache"""
        with self.lock:
            self.cache.clear()
            self.timestamps.clear()

# Global cache instance
cache = NonBlockingCache()

def cached_operation(cache_key_func, ttl=300):
    """Decorator for caching operation results"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            cache_key = cache_key_func(*args, **kwargs)
            
            # Try to get from cache first
            cached_result = cache.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # Execute operation and cache result
            result = func(*args, **kwargs)
            cache.set(cache_key, result)
            return result
        return wrapper
    return decorator

def batch_database_operations(operations, batch_size=10):
    """Execute database operations in batches to improve performance"""
    results = []
    
    for i in range(0, len(operations), batch_size):
        batch = operations[i:i + batch_size]
        batch_futures = []
        
        for operation, args, kwargs in batch:
            future = executor.submit(operation, *args, **kwargs)
            batch_futures.append(future)
        
        # Wait for batch to complete
        for future in as_completed(batch_futures, timeout=10):
            try:
                result = future.result()
                results.append(result)
            except Exception as e:
                logging.error(f"Batch operation failed: {e}")
                results.append(None)
    
    return results

class RateLimiter:
    """Rate limiter to prevent abuse"""
    
    def __init__(self, max_requests=10, time_window=60):
        self.max_requests = max_requests
        self.time_window = time_window
        self.requests = {}
        self.lock = threading.RLock()
    
    def is_allowed(self, user_id):
        """Check if user is allowed to make a request"""
        with self.lock:
            current_time = time.time()
            
            if user_id not in self.requests:
                self.requests[user_id] = []
            
            # Remove old requests
            self.requests[user_id] = [
                req_time for req_time in self.requests[user_id]
                if current_time - req_time < self.time_window
            ]
            
            # Check if under limit
            if len(self.requests[user_id]) < self.max_requests:
                self.requests[user_id].append(current_time)
                return True
            
            return False

# Global rate limiter
rate_limiter = RateLimiter()

def rate_limited(func):
    """Decorator for rate limiting"""
    @wraps(func)
    def wrapper(update, context, *args, **kwargs):
        user_id = update.effective_user.id
        
        if not rate_limiter.is_allowed(user_id):
            update.message.reply_text("⚠️ Too many requests. Please wait a moment before trying again.")
            return
        
        return func(update, context, *args, **kwargs)
    return wrapper

def cleanup_resources():
    """Cleanup resources on shutdown"""
    executor.shutdown(wait=True)
    
    # Close all database connections
    while not db_pool.empty():
        try:
            conn = db_pool.get_nowait()
            conn.close()
        except queue.Empty:
            break
    
    cache.clear()

# Initialize on import
init_db_pool()
