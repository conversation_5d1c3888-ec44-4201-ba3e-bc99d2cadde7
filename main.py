import sys
# import threading  # Added import
sys.path.append('/helpers')

from helpers.buttons import *
from helpers.commands import setup_commands
from helpers.utils import clear_cached_payments  # Import clear_cached_payments
from helpers.config import BOT_TOKEN  # Import the bot token

# from callbackAPI import app  # Added import

logging.basicConfig(format='%(asctime)s -%(levelname)s - %(message)s', level=logging.INFO)

def clear_cache_job(context: CallbackContext):
    clear_cached_payments()
    logging.info("Cache cleared by scheduled job.")

def main() -> None:
    init_db()
    
    # # Start Flask app in a separate thread
    # flask_thread = threading.Thread(target=app.run, kwargs={'host':'0.0.0.0', 'port':5000}, daemon=True)
    # flask_thread.start()
    
    updater = Updater(BOT_TOKEN)  # Replace with your bot token
    dispatcher = updater.dispatcher
    setup_commands(dispatcher)
    dispatcher.add_handler(CallbackQ<PERSON>yHandler(button_callback))
    dispatcher.add_handler(MessageHandler(Filters.text & ~Filters.command, handle_message))
    dispatcher.bot_data['job_queue'] = updater.job_queue
    
    # Schedule cache clearing job every 30 minutes
    job_queue = updater.job_queue
    job_queue.run_repeating(clear_cache_job, interval=clear_cache_interval, first=-1)  # 1800 seconds = 30 minutes
    
    updater.start_polling()
    updater.idle()

if __name__ == '__main__':
    main()
    #test