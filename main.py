import sys
import threading
import signal
import atexit
sys.path.append('/helpers')

from helpers.buttons import *
from helpers.commands import setup_commands
from helpers.utils import clear_cached_payments  # Import clear_cached_payments
from helpers.config import BOT_TOKEN  # Import the bot token
from helpers.data import is_bot_activated, log_to_developers  # Import activation functions
from helpers.concurrent_handler import cleanup_resources, user_handler  # Import concurrent handling
from helpers.remote_control import start_remote_control, stop_remote_control, is_bot_active  # Import remote control

# from callbackAPI import app  # Added import

logging.basicConfig(format='%(asctime)s -%(levelname)s - %(message)s', level=logging.INFO)

def clear_cache_job(context: CallbackContext):
    clear_cached_payments()
    logging.info("Cache cleared by scheduled job.")

def signal_handler(signum, frame):
    """Handle shutdown signals gracefully"""
    print("\n🛑 Shutting down bot...")
    log_to_developers("BOT_STOP", "Bot shutting down", additional_data="Graceful shutdown")
    stop_remote_control()
    cleanup_resources()
    sys.exit(0)

def main() -> None:
    # Register signal handlers for graceful shutdown
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    atexit.register(cleanup_resources)

    init_db()

    # Check if bot is activated before starting
    if not is_bot_activated():
        print("❌ Bot is not activated!")
        print("Please provide your activation code to start the bot.")
        print("Contact the developers to get your activation code.")
        log_to_developers("ACTIVATION_ATTEMPT", "Bot startup attempted without activation", additional_data="Bot not activated")
        return

    log_to_developers("BOT_START", "Bot started successfully", additional_data="Bot activated and running")

    # Start remote control system
    start_remote_control()

    # # Start Flask app in a separate thread
    # flask_thread = threading.Thread(target=app.run, kwargs={'host':'0.0.0.0', 'port':5000}, daemon=True)
    # flask_thread.start()

    # Configure updater for high concurrency
    updater = Updater(BOT_TOKEN, workers=32)  # Increase worker threads for better concurrency
    dispatcher = updater.dispatcher

    # Set up handlers
    setup_commands(dispatcher)
    dispatcher.add_handler(CallbackQueryHandler(button_callback))
    dispatcher.add_handler(MessageHandler(Filters.text & ~Filters.command, handle_message))
    dispatcher.bot_data['job_queue'] = updater.job_queue

    # Schedule cache clearing job every 30 minutes
    job_queue = updater.job_queue
    job_queue.run_repeating(clear_cache_job, interval=clear_cache_interval, first=-1)

    print("🚀 Bot started successfully! Ready to handle up to 500 concurrent users.")
    print("Press Ctrl+C to stop the bot.")

    try:
        updater.start_polling(
            poll_interval=0.1,  # Faster polling for better responsiveness
            timeout=10,
            read_latency=2.0,
            bootstrap_retries=-1  # Infinite retries
        )
        updater.idle()
    except KeyboardInterrupt:
        print("\n🛑 Bot stopped by user")
    finally:
        cleanup_resources()

if __name__ == '__main__':
    main()
    #test