#!/usr/bin/env python3
"""
Bot Activation Script
This script allows customers to activate their bot using the activation code provided by developers.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'helpers'))

from helpers.data import init_db, activate_bot, is_bot_activated, get_activation_status, log_to_developers
import socket
import platform

def get_bot_instance_id():
    """Generate a unique bot instance ID based on system information"""
    hostname = socket.gethostname()
    system = platform.system()
    machine = platform.machine()
    return f"{hostname}_{system}_{machine}"

def main():
    print("🤖 ServiceBot Activation System")
    print("=" * 40)
    
    # Initialize database
    init_db()
    
    # Check if already activated
    if is_bot_activated():
        status = get_activation_status()
        print("✅ Bot is already activated!")
        print(f"Activation Code: {status['activation_code']}")
        print(f"Activated At: {status['activated_at']}")
        print(f"Bot Instance ID: {status['bot_instance_id']}")
        if status['customer_info']:
            print(f"Customer Info: {status['customer_info']}")
        print("\nYour bot is ready to use. Run 'python main.py' to start.")
        return
    
    print("❌ Bot is not activated.")
    print("Please enter your activation code to activate the bot.")
    print("Contact the developers if you don't have an activation code.")
    print()
    
    # Get activation code from user
    activation_code = input("Enter your activation code: ").strip()
    
    if not activation_code:
        print("❌ No activation code provided. Exiting.")
        return
    
    # Get optional customer information
    print("\nOptional: Provide some information about your setup (press Enter to skip):")
    customer_name = input("Your name/company: ").strip()
    customer_email = input("Your email: ").strip()
    customer_purpose = input("Bot usage purpose: ").strip()
    
    customer_info = None
    if customer_name or customer_email or customer_purpose:
        customer_info = f"Name: {customer_name}, Email: {customer_email}, Purpose: {customer_purpose}"
    
    # Generate bot instance ID
    bot_instance_id = get_bot_instance_id()
    
    print(f"\nBot Instance ID: {bot_instance_id}")
    print("Attempting to activate bot...")
    
    # Try to activate
    if activate_bot(activation_code, bot_instance_id, customer_info):
        print("✅ Bot activated successfully!")
        print("Your bot is now ready to use.")
        print("Run 'python main.py' to start the bot.")
        
        # Log successful activation
        log_to_developers(
            "BOT_ACTIVATED", 
            f"Bot activated with code: {activation_code}", 
            additional_data=f"Instance: {bot_instance_id}, Customer: {customer_info}"
        )
    else:
        print("❌ Activation failed!")
        print("Possible reasons:")
        print("- Invalid activation code")
        print("- Activation code already used")
        print("- Database error")
        print("\nPlease contact the developers for support.")
        
        # Log failed activation attempt
        log_to_developers(
            "ACTIVATION_FAILED", 
            f"Failed activation attempt with code: {activation_code}", 
            additional_data=f"Instance: {bot_instance_id}, Customer: {customer_info}"
        )

if __name__ == "__main__":
    main()
