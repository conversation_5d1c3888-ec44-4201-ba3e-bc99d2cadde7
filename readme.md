# ServiceBot - Professional Telegram Order Management Bot

## 1. Introduction

**ServiceBot** is a professional Telegram bot designed to handle all your order management needs efficiently and securely. This bot automates the entire order process from customer inquiries to payment processing and order fulfillment.

### Key Features:
- 🤖 **Automated Order Processing** - Handles customer orders from start to finish
- 💰 **Multi-Cryptocurrency Support** - Accepts Bitcoin, Ethereum, Litecoin, and Solana
- 🏪 **Multi-Store Management** - Support for multiple stores (Nike, Adidas, Zara, H&M, Gucci)
- 🔒 **Secure Payment Processing** - Integrated with CryptAPI for secure transactions
- 📊 **Real-time Order Tracking** - Live status updates and payment confirmations
- 👥 **Concurrent User Support** - Handles up to 500 simultaneous users
- 🛡️ **Advanced Security** - Encrypted codebase with activation system
- 📈 **Developer Analytics** - Built-in logging and monitoring system

### What This Bot Does:
- Receives and processes customer orders through Telegram
- Manages store-specific requirements and configurations
- Generates secure payment addresses for each transaction
- Monitors payments and confirms transactions automatically
- Sends order details to designated admin groups
- Provides real-time status updates to customers
- Handles order approvals and rejections
- Manages upfront and on-confirmation payment types

---

## 2. Requirements for Setup

### System Requirements:
- **Operating System**: Linux (Ubuntu 20.04+ recommended), Windows 10+, or macOS 10.15+
- **Python**: Version 3.8 or higher
- **RAM**: Minimum 1GB, recommended 2GB+
- **Storage**: Minimum 5GB free space
- **Internet**: Stable broadband connection

### Software Requirements:
- **Python 3.8+** with pip package manager
- **Git** (for cloning and updates)
- **Text Editor** (VS Code, Sublime Text, or similar)
- **Terminal/Command Prompt** access

### Telegram Requirements:
- **Bot Token** from @BotFather
- **Telegram Groups** for order management
- **Admin Account** with group management permissions

### Payment Processing:
- **CryptAPI Account** (free registration)
- **Cryptocurrency Wallets** for each supported coin
- **Valid Wallet Addresses** for receiving payments

### Technical Knowledge:
- Basic command line usage
- Text file editing
- Understanding of Telegram bot setup
- Basic cryptocurrency wallet management

---

## 3. VPS Recommendation

For optimal performance and 24/7 uptime, we **strongly recommend** using a Virtual Private Server (VPS).

### Recommended VPS Provider: Hostinger

We recommend purchasing a VPS through **Hostinger.com** using our affiliate link for the best experience:

**🔗 [Get Hostinger VPS with Special Discount](https://hostinger.com/vps-hosting?ref=servicebot)**

### Recommended VPS Specifications:
- **Plan**: VPS 1 or higher
- **CPU**: 1 vCPU minimum, 2 vCPU recommended
- **RAM**: 2GB minimum, 4GB recommended
- **Storage**: 40GB SSD minimum
- **Bandwidth**: Unlimited or 1TB+
- **Operating System**: Ubuntu 20.04 LTS

### Alternative VPS Providers:
- **DigitalOcean** - $5/month droplet
- **Vultr** - $5/month instance
- **Linode** - $5/month nanode
- **AWS EC2** - t3.micro instance
- **Google Cloud** - e2-micro instance

### VPS Setup Benefits:
- ✅ 24/7 uptime for your bot
- ✅ Better performance and reliability
- ✅ Professional appearance to customers
- ✅ Scalability for growing business
- ✅ Remote access from anywhere
- ✅ Automatic backups available

---

## 4. Installation and Setup

### Step 1: VPS Initial Setup

Connect to your VPS via SSH:
```bash
ssh root@your-vps-ip-address
```

Update your system:
```bash
apt update && apt upgrade -y
```

Install required packages:
```bash
apt install python3 python3-pip git nano curl wget -y
```

### Step 2: Download ServiceBot

Create a directory for your bot:
```bash
mkdir /opt/servicebot
cd /opt/servicebot
```

Extract your ServiceBot package (provided by developers):
```bash
# Upload your servicebot package to the VPS
# Then extract it:
unzip servicebot-package.zip
cd servicebot
```

### Step 3: Install Dependencies

Install Python dependencies:
```bash
pip3 install -r requirements.txt
```

### Step 4: Bot Activation

Activate your bot with the provided activation code:
```bash
python3 activate.py
```

Enter your activation code when prompted. Contact the developers if you don't have an activation code.

### Step 5: Configuration

Edit the configuration file:
```bash
nano helpers/config.py
```

**Important Configuration Steps:**

1. **Set your Bot Token:**
   ```python
   BOT_TOKEN = "YOUR_BOT_TOKEN_HERE"
   ```

2. **Configure Crypto Addresses:**
   ```python
   crypto_addresses = {
       "btc": "your_bitcoin_address",
       "eth": "your_ethereum_address",
       "ltc": "your_litecoin_address",
       "sol/sol": "your_solana_address",
   }
   ```

3. **Set Group Chat IDs:**
   ```python
   REQUEST_GROUP_CHAT_IDS = {
       "Nike": "-1001234567890",
       "Adidas": "-1001234567890",
       # ... add your group IDs
   }
   ```

4. **Configure Callback URL:**
   ```python
   CALLBACK_BASE_URL = "http://your-vps-ip:5000/callback"
   ```

### Step 6: Get Telegram Group IDs

1. Create your order management groups in Telegram
2. Add @getmyid_bot to each group
3. The bot will send you the group ID
4. Copy these IDs to your config.py file

### Step 7: Start the Bot

Start your ServiceBot:
```bash
python3 start_bot.py
```

For background operation:
```bash
nohup python3 start_bot.py > bot.log 2>&1 &
```

### Step 8: Test Your Bot

1. Send `/start` to your bot in Telegram
2. Test the order process
3. Verify group notifications work
4. Test payment processing with small amounts

---

## 5. All Done!

**🎉 Congratulations! Your ServiceBot is now running and ready to handle orders!**

Your bot is now:
- ✅ Activated and running
- ✅ Configured for your business
- ✅ Ready to accept orders
- ✅ Processing payments automatically
- ✅ Sending notifications to your groups

### What's Next?

1. **Test thoroughly** with small orders first
2. **Monitor the logs** to ensure everything works
3. **Customize store settings** as needed
4. **Set up monitoring** for 24/7 operation
5. **Scale up** as your business grows

### Need Help?

If you have any questions or encounter issues, feel free to contact our support team:

**📧 Email:** <EMAIL>
**💬 Telegram:** @ServiceBotSupport
**📱 Phone:** +1-XXX-XXX-XXXX

### Support Information to Provide:
- Your activation code (first 8 characters only)
- Error messages (exact text)
- Steps that led to the issue
- Your system information

### Response Times:
- **Critical Issues:** Within 2 hours
- **General Support:** Within 24 hours
- **Feature Requests:** Within 48 hours

---

**Thank you for choosing ServiceBot! We're here to support your success.**

*ServiceBot v2.0 - Professional Telegram Bot Solution*
*© 2025 ServiceBot Developers. All rights reserved.*

## Services
List of all the services
=> no changes needed.

## FaQ
Frequently asked questions
=> no changes needed.

## ToS
Terms of service
=> no changes needed.

## My orders
List of all the orders
=> **CHANGES NEEDED**

Everytime the bot needs to send something to the client, it will be this message:
"Hello {client_name}, there is an update on your order: {order_id}."
"Please check the status of your order via /myorders."

The order status message will always look like this:
"Status: {status}"
"Standard order details message"

### DECLINED
##### Order Declined:
###### Text:
"Unfortunately, your order has been declined. Please contact the admin for further details or assistance."

###### Buttons
1. "Contact admin"
2. "Okay no problem, remove this order out of the database."

### APPROVED

#### ON CONFIRMATION PAYMENT

##### Order Approved:
###### Text:
"Good news! Your order has been approved. Payment will be required upon confirmation of your order. No action is needed right now—simply wait for the next status update."

###### Buttons
1. "Contact admin"

##### Order ready:
###### Text:
"Your order has been confirmed and now requires payment. Please proceed with the payment of {amount} {currency}. Make sure you pay on-time to avoid any problems."

###### Buttons
1. "Contact admin"
2. "Check the available cryptocurrencies"
3. "I'm ready to pay"

##### Order cancel:
###### Text:
"Your order has been cancelled. Please contact the admin for further details or assistance."

###### Buttons
1. "Contact admin"
2. "Okay no problem, remove this order out of the database."


#### UPFRONT PAYMENT

##### Order Approved:
###### Text:
"Great news! Your order has been approved. However, payment of {amount} {currency} is required upfront to proceed. If you choose not to pay upfront, the order cannot be processed."

###### Buttons
1. "Contact admin"
2. "Check the available cryptocurrencies"
3. "I'm ready to pay"

##### Order ready:
###### Text:
"Your order has been confirmed and is now done. Because you have paid upfront, no further action is needed. Thank you for using our services."

###### Buttons
1. "Contact admin"
2. "Thank you! Remove this order from the database."






TODO:
Delete entire chat history user after final confirm of order created


upfront:

bij payment amount entered and confirmed send Order approved + amount + currency to user