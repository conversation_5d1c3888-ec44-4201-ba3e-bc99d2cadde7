# New Idea

1. <PERSON><PERSON> starts the bot with /start
2. <PERSON><PERSON> chooses between [Services], [FaQ], [ToS], [My orders]

## Services
List of all the services
=> no changes needed.

## FaQ
Frequently asked questions
=> no changes needed.

## ToS
Terms of service
=> no changes needed.

## My orders
List of all the orders
=> **CHANGES NEEDED**

Everytime the bot needs to send something to the client, it will be this message:
"Hello {client_name}, there is an update on your order: {order_id}."
"Please check the status of your order via /myorders."

The order status message will always look like this:
"Status: {status}"
"Standard order details message"

### DECLINED
##### Order Declined:
###### Text:
"Unfortunately, your order has been declined. Please contact the admin for further details or assistance."

###### Buttons
1. "Contact admin"
2. "Okay no problem, remove this order out of the database."

### APPROVED

#### ON CONFIRMATION PAYMENT

##### Order Approved:
###### Text:
"Good news! Your order has been approved. Payment will be required upon confirmation of your order. No action is needed right now—simply wait for the next status update."

###### Buttons
1. "Contact admin"

##### Order ready:
###### Text:
"Your order has been confirmed and now requires payment. Please proceed with the payment of {amount} {currency}. Make sure you pay on-time to avoid any problems."

###### Buttons
1. "Contact admin"
2. "Check the available cryptocurrencies"
3. "I'm ready to pay"

##### Order cancel:
###### Text:
"Your order has been cancelled. Please contact the admin for further details or assistance."

###### Buttons
1. "Contact admin"
2. "Okay no problem, remove this order out of the database."


#### UPFRONT PAYMENT

##### Order Approved:
###### Text:
"Great news! Your order has been approved. However, payment of {amount} {currency} is required upfront to proceed. If you choose not to pay upfront, the order cannot be processed."

###### Buttons
1. "Contact admin"
2. "Check the available cryptocurrencies"
3. "I'm ready to pay"

##### Order ready:
###### Text:
"Your order has been confirmed and is now done. Because you have paid upfront, no further action is needed. Thank you for using our services."

###### Buttons
1. "Contact admin"
2. "Thank you! Remove this order from the database."






TODO:
Delete entire chat history user after final confirm of order created


upfront:

bij payment amount entered and confirmed send Order approved + amount + currency to user