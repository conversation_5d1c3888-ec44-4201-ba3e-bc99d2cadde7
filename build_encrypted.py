#!/usr/bin/env python3
"""
PyArmor Build Script for ServiceBot
This script encrypts all Python files except config.py for distribution to customers.
"""

import os
import shutil
import subprocess
import sys
from pathlib import Path

# Configuration
SOURCE_DIR = "."
BUILD_DIR = "dist_encrypted"
EXCLUDE_FILES = [
    "helpers/config.py",  # Customer configuration file
    "build_encrypted.py",  # This build script
    "dev_tools.py",  # Developer tools
    "admin_bot.py",  # Admin bot
    "activate_bot.py",  # Activation script (will be encrypted separately)
]

EXCLUDE_DIRS = [
    "dist",
    "dist_encrypted", 
    "__pycache__",
    ".git",
    ".vscode",
    "node_modules"
]

def check_pyarmor():
    """Check if PyArmor is installed"""
    try:
        result = subprocess.run(['pyarmor', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ PyArmor found: {result.stdout.strip()}")
            return True
        else:
            print("❌ PyArmor not found or not working")
            return False
    except FileNotFoundError:
        print("❌ PyArmor not installed")
        return False

def install_pyarmor():
    """Install PyArmor"""
    print("📦 Installing PyArmor...")
    try:
        subprocess.run([sys.executable, '-m', 'pip', 'install', 'pyarmor'], check=True)
        print("✅ PyArmor installed successfully")
        return True
    except subprocess.CalledProcessError:
        print("❌ Failed to install PyArmor")
        return False

def clean_build_dir():
    """Clean the build directory"""
    if os.path.exists(BUILD_DIR):
        print(f"🧹 Cleaning {BUILD_DIR}...")
        shutil.rmtree(BUILD_DIR)
    os.makedirs(BUILD_DIR, exist_ok=True)

def copy_unencrypted_files():
    """Copy files that should not be encrypted"""
    print("📄 Copying unencrypted files...")
    
    # Copy config.py
    config_src = "helpers/config.py"
    config_dst = os.path.join(BUILD_DIR, "helpers", "config.py")
    os.makedirs(os.path.dirname(config_dst), exist_ok=True)
    shutil.copy2(config_src, config_dst)
    print(f"  ✅ Copied {config_src}")
    
    # Copy other necessary files
    files_to_copy = [
        "requirements.txt",
        "README.md",
        "dev_log_config.json"
    ]
    
    for file in files_to_copy:
        if os.path.exists(file):
            dst = os.path.join(BUILD_DIR, file)
            shutil.copy2(file, dst)
            print(f"  ✅ Copied {file}")

def get_python_files():
    """Get list of Python files to encrypt"""
    python_files = []
    
    for root, dirs, files in os.walk(SOURCE_DIR):
        # Skip excluded directories
        dirs[:] = [d for d in dirs if d not in EXCLUDE_DIRS]
        
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.relpath(os.path.join(root, file), SOURCE_DIR)
                
                # Skip excluded files
                if file_path not in EXCLUDE_FILES and not file_path.startswith('dist'):
                    python_files.append(file_path)
    
    return python_files

def encrypt_files():
    """Encrypt Python files with PyArmor"""
    print("🔐 Encrypting Python files...")
    
    python_files = get_python_files()
    
    if not python_files:
        print("❌ No Python files found to encrypt")
        return False
    
    print(f"📝 Found {len(python_files)} files to encrypt:")
    for file in python_files:
        print(f"  - {file}")
    
    # Create PyArmor project
    try:
        # Initialize PyArmor
        subprocess.run([
            'pyarmor', 'gen', 
            '--output', BUILD_DIR,
            '--recursive',
            '--exclude', 'helpers/config.py',
            '--exclude', 'build_encrypted.py',
            '--exclude', 'dev_tools.py', 
            '--exclude', 'admin_bot.py',
            '.'
        ], check=True, cwd=SOURCE_DIR)
        
        print("✅ Files encrypted successfully")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Encryption failed: {e}")
        return False

def create_startup_script():
    """Create a startup script for customers"""
    startup_script = """#!/usr/bin/env python3
\"\"\"
ServiceBot Startup Script
Run this script to start your ServiceBot instance.
\"\"\"

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    # Import and run the main bot
    from main import main
    
    if __name__ == '__main__':
        print("🤖 Starting ServiceBot...")
        print("Make sure you have configured helpers/config.py before running!")
        print("Press Ctrl+C to stop the bot.")
        print("-" * 50)
        main()
        
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure all dependencies are installed: pip install -r requirements.txt")
except KeyboardInterrupt:
    print("\\n👋 Bot stopped by user")
except Exception as e:
    print(f"❌ Error starting bot: {e}")
    print("Check your configuration in helpers/config.py")
"""
    
    script_path = os.path.join(BUILD_DIR, "start_bot.py")
    with open(script_path, 'w') as f:
        f.write(startup_script)
    
    print("✅ Created startup script: start_bot.py")

def create_activation_script():
    """Create encrypted activation script"""
    print("🔐 Creating encrypted activation script...")
    
    try:
        # Encrypt the activation script separately
        subprocess.run([
            'pyarmor', 'gen',
            '--output', os.path.join(BUILD_DIR, 'encrypted_activate'),
            'activate_bot.py'
        ], check=True)
        
        # Create a wrapper script
        wrapper_script = """#!/usr/bin/env python3
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'encrypted_activate'))
from activate_bot import main

if __name__ == '__main__':
    main()
"""
        
        wrapper_path = os.path.join(BUILD_DIR, "activate.py")
        with open(wrapper_path, 'w') as f:
            f.write(wrapper_script)
        
        print("✅ Created encrypted activation script")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to encrypt activation script: {e}")
        return False

def create_customer_readme():
    """Create README for customers"""
    readme_content = """# ServiceBot - Customer Installation

## Quick Start

1. **Activate your bot:**
   ```bash
   python activate.py
   ```
   Enter the activation code provided by the developers.

2. **Configure your bot:**
   Edit `helpers/config.py` with your settings:
   - Crypto wallet addresses
   - Telegram group chat IDs
   - Bot token
   - Store configurations

3. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

4. **Start your bot:**
   ```bash
   python start_bot.py
   ```

## Important Notes

- Only modify `helpers/config.py` - all other files are encrypted
- Keep your activation code secure
- Contact developers for support

## Support

If you encounter any issues, contact the developers with:
- Your activation code (first 8 characters only)
- Error messages
- Description of the problem

---
*ServiceBot - Professional Telegram Bot Solution*
"""
    
    readme_path = os.path.join(BUILD_DIR, "CUSTOMER_README.md")
    with open(readme_path, 'w') as f:
        f.write(readme_content)
    
    print("✅ Created customer README")

def main():
    """Main build function"""
    print("🔨 ServiceBot PyArmor Build Script")
    print("=" * 40)
    
    # Check PyArmor
    if not check_pyarmor():
        if not install_pyarmor():
            print("❌ Cannot proceed without PyArmor")
            return False
    
    # Clean build directory
    clean_build_dir()
    
    # Copy unencrypted files
    copy_unencrypted_files()
    
    # Encrypt files
    if not encrypt_files():
        return False
    
    # Create additional scripts
    create_startup_script()
    create_activation_script()
    create_customer_readme()
    
    print("\n✅ Build completed successfully!")
    print(f"📦 Encrypted bot package created in: {BUILD_DIR}")
    print("\nNext steps:")
    print("1. Test the encrypted bot")
    print("2. Create activation codes using dev_tools.py")
    print("3. Package and distribute to customers")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
