#!/usr/bin/env python3
"""
Test Script for Encrypted ServiceBot Build
This script tests the encrypted build to ensure it works correctly.
"""

import os
import sys
import subprocess
import tempfile
import shutil
from pathlib import Path

BUILD_DIR = "dist_encrypted"

def test_file_structure():
    """Test that the build has the correct file structure"""
    print("📁 Testing file structure...")
    
    required_files = [
        "helpers/config.py",  # Should be unencrypted
        "start_bot.py",       # Startup script
        "activate.py",        # Activation script
        "requirements.txt",   # Dependencies
        "CUSTOMER_README.md", # Customer documentation
    ]
    
    required_dirs = [
        "helpers",
        "pyarmor_runtime_000000",  # PyArmor runtime
    ]
    
    missing_files = []
    missing_dirs = []
    
    for file in required_files:
        file_path = os.path.join(BUILD_DIR, file)
        if not os.path.exists(file_path):
            missing_files.append(file)
        else:
            print(f"  ✅ {file}")
    
    for dir in required_dirs:
        dir_path = os.path.join(BUILD_DIR, dir)
        if not os.path.exists(dir_path):
            missing_dirs.append(dir)
        else:
            print(f"  ✅ {dir}/")
    
    if missing_files or missing_dirs:
        print("❌ Missing files/directories:")
        for file in missing_files:
            print(f"  - {file}")
        for dir in missing_dirs:
            print(f"  - {dir}/")
        return False
    
    print("✅ File structure test passed")
    return True

def test_config_unencrypted():
    """Test that config.py is not encrypted"""
    print("🔍 Testing config.py encryption status...")
    
    config_path = os.path.join(BUILD_DIR, "helpers", "config.py")
    
    if not os.path.exists(config_path):
        print("❌ config.py not found")
        return False
    
    # Read the file and check if it's readable Python code
    try:
        with open(config_path, 'r') as f:
            content = f.read()
        
        # Check for PyArmor markers
        if "pyarmor" in content.lower() or "__pyarmor__" in content:
            print("❌ config.py appears to be encrypted")
            return False
        
        # Check for expected configuration variables
        expected_vars = ["BOT_TOKEN", "crypto_addresses", "stores"]
        found_vars = []
        
        for var in expected_vars:
            if var in content:
                found_vars.append(var)
        
        if len(found_vars) >= 2:  # At least 2 expected variables found
            print("✅ config.py is unencrypted and contains expected variables")
            return True
        else:
            print("❌ config.py doesn't contain expected variables")
            return False
            
    except Exception as e:
        print(f"❌ Error reading config.py: {e}")
        return False

def test_main_encrypted():
    """Test that main.py is encrypted"""
    print("🔐 Testing main.py encryption status...")
    
    main_path = os.path.join(BUILD_DIR, "main.py")
    
    if not os.path.exists(main_path):
        print("❌ main.py not found")
        return False
    
    try:
        with open(main_path, 'r') as f:
            content = f.read()
        
        # Check for PyArmor markers
        if "__pyarmor__" in content or "pyarmor_runtime" in content:
            print("✅ main.py is encrypted")
            return True
        else:
            print("❌ main.py doesn't appear to be encrypted")
            return False
            
    except Exception as e:
        print(f"❌ Error reading main.py: {e}")
        return False

def test_import_structure():
    """Test that the encrypted modules can be imported"""
    print("📦 Testing import structure...")
    
    # Create a temporary test script
    test_script = f"""
import sys
import os
sys.path.insert(0, r'{os.path.abspath(BUILD_DIR)}')

try:
    # Test importing encrypted modules
    from helpers import data
    from helpers import utils
    from helpers import settings
    print("✅ Successfully imported encrypted modules")
    
    # Test that config is accessible
    from helpers.config import BOT_TOKEN
    print("✅ Successfully imported from unencrypted config")
    
except ImportError as e:
    print(f"❌ Import error: {{e}}")
    sys.exit(1)
except Exception as e:
    print(f"❌ Unexpected error: {{e}}")
    sys.exit(1)
"""
    
    # Write and run the test script
    with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
        f.write(test_script)
        test_script_path = f.name
    
    try:
        result = subprocess.run([sys.executable, test_script_path], 
                              capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ Import structure test passed")
            print(result.stdout.strip())
            return True
        else:
            print("❌ Import structure test failed")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Import test timed out")
        return False
    except Exception as e:
        print(f"❌ Error running import test: {e}")
        return False
    finally:
        # Clean up
        try:
            os.unlink(test_script_path)
        except:
            pass

def test_activation_script():
    """Test that the activation script works"""
    print("🔑 Testing activation script...")
    
    activate_path = os.path.join(BUILD_DIR, "activate.py")
    
    if not os.path.exists(activate_path):
        print("❌ activate.py not found")
        return False
    
    # Test that the script can be executed (but don't actually activate)
    test_script = f"""
import sys
sys.path.insert(0, r'{os.path.abspath(BUILD_DIR)}')

try:
    # Import the activation module
    import activate
    print("✅ Activation script imports successfully")
except ImportError as e:
    print(f"❌ Activation script import error: {{e}}")
    sys.exit(1)
except Exception as e:
    print(f"❌ Activation script error: {{e}}")
    sys.exit(1)
"""
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
        f.write(test_script)
        test_script_path = f.name
    
    try:
        result = subprocess.run([sys.executable, test_script_path], 
                              capture_output=True, text=True, timeout=15)
        
        if result.returncode == 0:
            print("✅ Activation script test passed")
            return True
        else:
            print("❌ Activation script test failed")
            print("STDERR:", result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Error testing activation script: {e}")
        return False
    finally:
        try:
            os.unlink(test_script_path)
        except:
            pass

def main():
    """Main test function"""
    print("🧪 ServiceBot Encrypted Build Test")
    print("=" * 40)
    
    if not os.path.exists(BUILD_DIR):
        print(f"❌ Build directory '{BUILD_DIR}' not found")
        print("Run build_encrypted.py first to create the encrypted build")
        return False
    
    tests = [
        ("File Structure", test_file_structure),
        ("Config Unencrypted", test_config_unencrypted),
        ("Main Encrypted", test_main_encrypted),
        ("Import Structure", test_import_structure),
        ("Activation Script", test_activation_script),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running: {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} failed")
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
    
    print("\n" + "=" * 40)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✅ All tests passed! The encrypted build is ready for distribution.")
        return True
    else:
        print("❌ Some tests failed. Please fix the issues before distributing.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
