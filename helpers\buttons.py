from helpers.utils import *

def handle_faq(update, context):
    query = update.callback_query
    query.edit_message_text(text=FAQ_TEXT, reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton(BACK_BUTTON_TEXT, callback_data='back')]]))

def handle_tos(update, context):
    query = update.callback_query
    query.edit_message_text(text=TOS_TEXT, reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton(BACK_BUTTON_TEXT, callback_data='back')]]))

def handle_clear_cache(update, context):
    query = update.callback_query
    clear_cached_payments()
    query.edit_message_text(text="Cache cleared.", reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton(BACK_BUTTON_TEXT, callback_data='back')]]))

def handle_services(update, context):
    query = update.callback_query
    if context.bot_data.get('stop_services'):
        keyboard = [[InlineKeyboardButton(BACK_BUTTON_TEXT, callback_data='back')]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        query.edit_message_text(text="Services are currently stopped.\n\nWe are most likely updating the bot.\n\nAll active orders will be completed and the bot will be restarted.", reply_markup=reply_markup)
        return
    else:
        # Get store names from REQUEST_GROUP_CHAT_IDS keys
        store_names = REQUEST_GROUP_CHAT_IDS.keys()
        
        # Create buttons dynamically
        keyboard = [
            [InlineKeyboardButton(store, callback_data=f'store_{store.lower()}')] 
            for store in store_names
        ]
        # Add back button
        keyboard.append([InlineKeyboardButton(BACK_BUTTON_TEXT, callback_data='back')])
        
        reply_markup = InlineKeyboardMarkup(keyboard)
        query.edit_message_text(text=SERVICES_TEXT, reply_markup=reply_markup)

def handle_back(update, context):
    query = update.callback_query
    if is_admin(query.from_user.id):
        keyboard = [
            [InlineKeyboardButton("Services", callback_data='services')],
            [InlineKeyboardButton("FAQ", callback_data='faq')],
            [InlineKeyboardButton("ToS", callback_data='tos')],
            [InlineKeyboardButton("My orders", callback_data='my_orders')],
            [InlineKeyboardButton("Admin options", callback_data='admin-options')],
        ]
    else:
        keyboard = [
            [InlineKeyboardButton("Services", callback_data='services')],
            [InlineKeyboardButton("FAQ", callback_data='faq')],
            [InlineKeyboardButton("ToS", callback_data='tos')],
            [InlineKeyboardButton("My orders", callback_data='my_orders')],  # Add "My orders" button
        ]
    reply_markup = InlineKeyboardMarkup(keyboard)
    query.edit_message_text(text=WELCOME_MESSAGE, reply_markup=reply_markup)

def handle_admin_menu(update, context):
    query = update.callback_query
    keyboard = [
        [InlineKeyboardButton("Clear cached payments", callback_data='clear-cache')],
        [InlineKeyboardButton("Stop services", callback_data='stop-services')],
        [InlineKeyboardButton("Enable services", callback_data='enable-services')],
        [InlineKeyboardButton("View Active Orders", callback_data='view_active_orders')],
        [InlineKeyboardButton("Order History", callback_data='view_inactive_orders')],
        [InlineKeyboardButton(BACK_BUTTON_TEXT, callback_data='back')],
    ]
    keyboard_2 = [
        [InlineKeyboardButton("Contact admin", callback_data='contact-admin_notAdminBug')],
        [InlineKeyboardButton(BACK_BUTTON_TEXT, callback_data='back')],
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)
    reply_markup_2 = InlineKeyboardMarkup(keyboard_2)
    if is_admin(query.from_user.id):
        query.edit_message_text(text="Admin menu", reply_markup=reply_markup)
    else:
        query.edit_message_text(text="You are not an admin, please report this to the admin for a bounty.", reply_markup=reply_markup_2)

def handle_view_active_orders(update, context):
    query = update.callback_query
    query.answer()
    
    if not is_admin(query.from_user.id):
        keyboard = [[InlineKeyboardButton(BACK_BUTTON_TEXT, callback_data='back')]]
        query.edit_message_text("❌ Access denied", reply_markup=InlineKeyboardMarkup(keyboard))
        return

    active_orders = get_active_orders()
    
    if not active_orders:
        query.edit_message_text("📭 No active orders found", reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton(BACK_BUTTON_TEXT, callback_data='back')]]))
        return

    message = "📋 *Active Orders*\n\n"
    for order in active_orders:
        payment_status = get_payment_status(order['order_id'], order['user_id'])
        message += (
            f"🆔 Order ID: `{order['order_id']}`\n"
            f"👤 User: {order['username']} ({order['user_id']})\n"
            f"🏬 Store: {order['store_name']}\n"
            f"📮 Status: {order['status'].replace('_', ' ').title()}\n"
            f"💸 Payment: {payment_status}\n"
            f"🔍 Check Details: `/order_info {order['order_id']} {order['user_id']}`\n"
            f"➖➖➖➖➖➖➖➖➖\n"
        )

    keyboard = [[InlineKeyboardButton(BACK_BUTTON_TEXT, callback_data='back')]]
    query.edit_message_text(message, parse_mode='Markdown', reply_markup=InlineKeyboardMarkup(keyboard))

def handle_view_inactive_orders(update, context):
    query = update.callback_query
    query.answer()
    
    if not is_admin(query.from_user.id):
        keyboard = [[InlineKeyboardButton(BACK_BUTTON_TEXT, callback_data='back')]]
        query.edit_message_text("❌ Access denied", reply_markup=InlineKeyboardMarkup(keyboard))
        return

    inactive_orders = get_inactive_orders()
    
    if not inactive_orders:
        query.edit_message_text("📭 No completed/canceled orders", reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton(BACK_BUTTON_TEXT, callback_data='back')]]))
        return

    message = "📚 *Order History* (Last 50)\n\n"
    for order in inactive_orders[:50]:  # Limit to last 50 entries
        payment_status = get_payment_status(order['order_id'], order['user_id'])
        message += (
            f"🆔 Order ID: `{order['order_id']}`\n"
            f"👤 User: {order['username']} ({order['user_id']})\n"
            f"🏬 Store: {order['store_name']}\n"
            f"📮 Status: {order['status'].replace('_', ' ').title()}\n"
            f"💸 Payment: {payment_status}\n"
            f"📅 Last Updated: {order['last_updated']}\n"
            f"🔍 Check Details: `/order_info {order['order_id']} {order['user_id']}`\n"
            f"➖➖➖➖➖➖➖➖➖\n"
        )

    keyboard = [[InlineKeyboardButton(BACK_BUTTON_TEXT, callback_data='back')]]
    query.edit_message_text(message, parse_mode='Markdown', reply_markup=InlineKeyboardMarkup(keyboard))

def handle_stop_services(update, context):
    query = update.callback_query
    keyboard = [
        [InlineKeyboardButton("Yes", callback_data='stop-services-confirm')],
        [InlineKeyboardButton(BACK_BUTTON_TEXT, callback_data='back')],
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)
    query.edit_message_text(text="Are you sure you want to stop the services?", reply_markup=reply_markup)

def handle_stop_services_confirm(update, context):
    query = update.callback_query
    context.bot_data['stop_services'] = True
    query.edit_message_text(text="Services stopped.", reply_markup=None)
    for admin in ADMIN_USER_IDS:
        try:
            context.bot.send_message(chat_id=admin, text="⚠️ Services stopped.")
        except BadRequest:
            pass
    logging.warning(f"\n{'-'*80}\n\nServices stopped.\n\n{'-'*80}\n")

def handle_enable_services(update, context):
    query = update.callback_query
    keyboard = [
        [InlineKeyboardButton("Yes", callback_data='enable-services-confirm')],
        [InlineKeyboardButton(BACK_BUTTON_TEXT, callback_data='back')],
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)
    query.edit_message_text(text="Are you sure you want to enable the services?", reply_markup=reply_markup)

def handle_enable_services_confirm(update, context):
    query = update.callback_query
    context.bot_data['stop_services'] = False
    query.edit_message_text(text="Services enabled.", reply_markup=None)
    for admin in ADMIN_USER_IDS:
        try:
            context.bot.send_message(chat_id=admin, text="⚠️ Services Started.")
        except BadRequest:
            pass
    logging.warning(f"\n{'-'*80}\n\nServices enabled.\n\n{'-'*80}\n")

def handle_startover(update, context):
    context.user_data['sent_all_info'] = False
    store_name = context.user_data.get('selected_store')
    if store_name:
        ask_required_info(update, context, store_name)
    delete_chat_history(update, context)  # Delete the entire chat history

def handle_my_orders(update, context):
    query = update.callback_query
    user_id = query.from_user.id
    confirmed_orders = get_confirmed_orders(user_id)
    if (confirmed_orders):
        keyboard = [[InlineKeyboardButton(order['order_id'], callback_data=f'order_{order["order_id"]}')] for order in confirmed_orders]
        keyboard.append([InlineKeyboardButton(BACK_BUTTON_TEXT, callback_data='back')])  # Add back button
        reply_markup = InlineKeyboardMarkup(keyboard)
        query.edit_message_text(text=MYORDER_MESSAGE_TEXT, reply_markup=reply_markup)
    else:
        keyboard = [[InlineKeyboardButton(BACK_BUTTON_TEXT, callback_data='back')]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        query.edit_message_text(
            text=NO_ORDERS_MESSAGE_TEXT,
            parse_mode='Markdown',
            reply_markup=reply_markup
        )

def handle_store_confirm(update, context):
    query = update.callback_query
    store_name = context.user_data.get('selected_store')
    logging.info(f"Confirming store: {store_name}")
    if store_name:
        query.delete_message()  # Remove the message containing the confirm button
        ask_required_info(update, context, store_name)

def handle_final_confirm(update, context):
    # Removed the line that incorrectly set 'sent_all_info' to False
    query = update.callback_query
    username = purify_markdown(query.from_user.username)  # Get the username of the user
    user_id = query.from_user.id
    store_name = context.user_data.get('selected_store')  # Get the store name
    logging.info(f"Final confirm store: {store_name}")  # Add logging to confirm store name at final confirmation
    chat_id = str(update.effective_chat.id)
    status_message = 'no_update'

    request_group_chat_id = REQUEST_GROUP_CHAT_IDS.get(store_name)
    info_responses = context.user_data.get('info_responses', {})  # Get the info responses
    order_id = generate_order_id()  # Generate an order ID

    # Purify the info_responses before saving
    purified_info_responses = {k: purify_markdown(v) for k, v in info_responses.items()}
    purified_username = purify_markdown(username)

    save_order(purified_username, user_id, store_name, json.dumps(purified_info_responses), order_id)  # Save the order to the database
    confirm_order(order_id)
    # Store the order_id in context.chat_data
    context.chat_data['pay_upfront_order_id'] = order_id

    # Put all the info from above into the database

    request_group_message = f"**New order request:**\n\n{get_order_details_message(order_id, username, user_id)}"
    request_group_keyboard = [
        [InlineKeyboardButton("Approve", callback_data=f'approve_{order_id}')],
        [InlineKeyboardButton("Decline", callback_data=f'decline_{order_id}_0')],
    ]
    if request_group_chat_id:
        sent_message = context.bot.send_message(
            chat_id=request_group_chat_id,
            text=request_group_message,
            reply_markup=InlineKeyboardMarkup(request_group_keyboard),
            parse_mode='Markdown'
        )
        # Store the message ID associated with this order
        context.chat_data.setdefault('start_order_messages', {}).setdefault(order_id, []).append(sent_message.message_id)
        # Store the message ID for 'Approve'/'Decline' buttons
        context.chat_data.setdefault('order_approve_decline_messages', {}).setdefault(order_id, []).append(sent_message.message_id)
        query.edit_message_text(ORDER_SUCCESS)
        context.user_data['order_id'] = order_id

        context.user_data['confirmation_message_id'] = query.message.message_id  # Store the message ID
        delete_chat_history(update, context)  # Delete the entire chat history except the confirmation message
        context.user_data.pop('confirmation_message_id', None)

            # Send the same order log to the ALL_LOGS_GROUP_CHAT_ID
    all_logs_message = f"**[ALL LOGS] New order request:**\n\n{get_order_details_message(order_id, username, user_id)}"
    context.bot.send_message(
        chat_id=ALL_LOGS_GROUP_CHAT_ID,
        text=all_logs_message,
        parse_mode='Markdown'
    )
    
    # Clear all user data related to the current order to prevent further processing
    keys_to_clear = ['required_info', 'current_info', 'info_responses', 'selected_store', 'sent_all_info']
    for key in keys_to_clear:
        context.user_data.pop(key, None)

def handle_cancel_order(update, context):
    query = update.callback_query
    query.answer()
    
    # Clear user data related to the order
    keys_to_clear = ['required_info', 'current_info', 'info_responses', 'selected_store', 'sent_all_info']
    for key in keys_to_clear:
        context.user_data.pop(key, None)
    
    delete_chat_history(update, context)

    # Return to the main menu
    if is_admin(query.from_user.id):
        keyboard = [
            [InlineKeyboardButton("Services", callback_data='services')],
            [InlineKeyboardButton("FAQ", callback_data='faq')],
            [InlineKeyboardButton("ToS", callback_data='tos')],
            [InlineKeyboardButton("My orders", callback_data='my_orders')],
            [InlineKeyboardButton("Admin options", callback_data='admin-options')],
        ]
    else:
        keyboard = [
            [InlineKeyboardButton("Services", callback_data='services')],
            [InlineKeyboardButton("FAQ", callback_data='faq')],
            [InlineKeyboardButton("ToS", callback_data='tos')],
            [InlineKeyboardButton("My orders", callback_data='my_orders')],  # Add "My orders" button
        ]
    reply_markup = InlineKeyboardMarkup(keyboard)
    context.bot.send_message(
        chat_id=query.message.chat_id,
        text="Your order has been canceled.\n\n" + WELCOME_MESSAGE,
        reply_markup=reply_markup
    )

def handle_store(update, context):
    query = update.callback_query
    store_name = query.data.replace('store_', '').capitalize()
    context.user_data['selected_store'] = store_name
    logging.info(f"Store selected: {store_name}")
    store_text = escape_markdown(get_store_text(store_name))
    keyboard = [
        [InlineKeyboardButton("Confirm", callback_data='confirm')],
        [InlineKeyboardButton(BACK_BUTTON_TEXT, callback_data='services')]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)
    query.edit_message_text(text=store_text, parse_mode='MarkdownV2', reply_markup=reply_markup)

def handle_order(update, context):
    query = update.callback_query
    order_id = query.data.split('_')[1]
    status_text, buttons_data = get_status_info(order_id)
    needed_amount = get_order_amount_needed(order_id)
    needed_currency = get_order_currency(order_id)
    message = f"📦 Order: {order_id}\n\n📝 Order details:\n- Store name: {get_store_name(order_id)}\n{get_order_details(order_id)}\n\n💡 Status:\n{status_text.format(amount=needed_amount, currency=needed_currency)}"
    
    keyboard = []
    for btn in buttons_data:
        raw_callback = btn["callback_data"]
        if "{order_id}" in raw_callback:
            custom_callback = raw_callback.format(order_id=order_id)
        else:
            custom_callback = raw_callback
        btn_text = btn["text"]
        keyboard.append([InlineKeyboardButton(btn_text, callback_data=custom_callback)])
    reply_markup = InlineKeyboardMarkup(keyboard)
    query.edit_message_text(text=message, parse_mode='Markdown', reply_markup=reply_markup)
    
def handle_contact_admin(update, context):
    query = update.callback_query
    order_id = query.data.split('_')[1]
    keyboard = [
        [InlineKeyboardButton(BACK_BUTTON_TEXT, callback_data=f'order_{order_id}')]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)
    query.edit_message_text(text=CONTACT_ADMIN, parse_mode='Markdown', reply_markup=reply_markup)

def handle_remove_order(update, context):
    query = update.callback_query
    order_id = query.data.split('_')[1]

    keyboard = [
        [InlineKeyboardButton("Yes", callback_data=f'remove-order-confirm_{order_id}')],
        [InlineKeyboardButton(BACK_BUTTON_TEXT, callback_data=f'order_{order_id}')]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)
    query.edit_message_text(text=REMOVE_ORDER, parse_mode='Markdown', reply_markup=reply_markup)

def handle_remove_order_confirm(update, context):
    query = update.callback_query
    order_id = query.data.split('_')[1]
    delete_order(order_id)
    keyboard = [
        [InlineKeyboardButton(BACK_BUTTON_TEXT, callback_data=f'my_orders')]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)
    query.edit_message_text(text=REMOVE_ORDER_CONFIRM, parse_mode='Markdown', reply_markup=reply_markup)

def handle_check_crypto(update, context):
    query = update.callback_query
    order_id = query.data.split('_')[1]
    keyboard = [
        [InlineKeyboardButton(BACK_BUTTON_TEXT, callback_data=f'order_{order_id}')]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)
    query.edit_message_text(text=CHECK_CRYPTO, parse_mode='Markdown', reply_markup=reply_markup)

def handle_approve(update, context):
    query = update.callback_query
    order_id = query.data.split('_')[1]
    username = get_order_username(order_id)
    user_id = get_order_user_id(order_id)

    context.chat_data.setdefault('order_approve_decline_messages', {})

    keyboard = [
        [InlineKeyboardButton("Upfront payment", callback_data=f'payment-upfront_{order_id}')],
        [InlineKeyboardButton("Payment on confirmation", callback_data=f'payment-confirmation_{order_id}')],
        [InlineKeyboardButton(BACK_BUTTON_TEXT, callback_data=f'back-decapp_{order_id}')]
    ]

    sent_message = query.edit_message_text(
        text=ORDER_APPROVED.format(
            order_id=order_id,
            order_details=get_order_details_message(order_id, username, user_id)
        ),
        reply_markup=InlineKeyboardMarkup(keyboard),
        parse_mode='Markdown'
    )
    context.chat_data.setdefault('start_order_messages', {}).setdefault(order_id, []).append(sent_message.message_id)
    context.chat_data['order_approve_decline_messages'].setdefault(order_id, []).append(sent_message.message_id)

def handle_decline(update, context):
    query = update.callback_query
    order_id = query.data.split('_')[1]
    confirmed = str(query.data.split('_')[2])
    username = get_order_username(order_id)
    user_id = get_order_user_id(order_id)

    keyboard = [
        [InlineKeyboardButton("Yes", callback_data=f'decline_{order_id}_1')],
        [InlineKeyboardButton(BACK_BUTTON_TEXT, callback_data=f'back-decapp_{order_id}')]
    ]

    reply_markup = InlineKeyboardMarkup(keyboard)

    if confirmed == '1':
        edit_status(order_id, 'declined')
        message = UPDATE_ORDER_MESSAGE.format(order_id=order_id, username=username)
        context.bot.send_message(chat_id=user_id, text=message, parse_mode='Markdown')
        query.edit_message_text(f"Order {order_id} has been declined.\n\n{get_order_details_message(order_id, username, user_id)}", parse_mode='Markdown')
    elif confirmed == '0':
        query.edit_message_text(f"Are you sure you want to decline this order?\n\n{get_order_details_message(order_id, username, user_id)}", parse_mode='Markdown', reply_markup=reply_markup)
    else:
        logging.error(f"Invalid decline confirmation: {confirmed}")

def handle_DecApp_back(update, context):
    query = update.callback_query
    order_id = query.data.split('_')[1]
    username = get_order_username(order_id)
    user_id = get_order_user_id(order_id)

    keyboard = [
        [InlineKeyboardButton("Approve", callback_data=f'approve_{order_id}')],
        [InlineKeyboardButton("Decline", callback_data=f'decline_{order_id}_0')],
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)
    message = f"**New order request:**\n\n{get_order_details_message(order_id, username, user_id)}"

    query.edit_message_text(message, reply_markup=reply_markup, parse_mode='Markdown')

def handle_payment_upfront(update, context):
    query = update.callback_query
    query.edit_message_reply_markup(reply_markup=None)  # Remove the keyboard
    order_id = query.data.split('_')[1]
    
    store_name = get_store_name(order_id)
    request_group_chat_id = REQUEST_GROUP_CHAT_IDS.get(store_name)

    pay_upfront_message = PAY_UPFRONT_MESSAGE
    sent_message = context.bot.send_message(chat_id=request_group_chat_id, text=pay_upfront_message, parse_mode='Markdown', reply_to_message_id=query.message.message_id)

    context.chat_data['pay_upfront_message_id'] = sent_message.message_id
    context.chat_data['pay_upfront_order_id'] = order_id

    context.chat_data.setdefault('start_order_messages', {}).setdefault(order_id, []).append(sent_message.message_id)
    context.chat_data.setdefault('payment_option_messages', {}).setdefault(order_id, []).append(query.message.message_id)

    update_order_payment_type(order_id, 'upfront')  # Save payment type in order data

def handle_payment_confirmation(update, context):
    query = update.callback_query
    order_id = query.data.split('_')[1]
    username = get_order_username(order_id)
    user_id = get_order_user_id(order_id)
    

    edit_status(order_id, 'approved_on_confirmation')
    message = UPDATE_ORDER_MESSAGE.format(order_id=order_id, username=username)
    context.bot.send_message(chat_id=user_id, text=message, parse_mode='Markdown')

    update_order_payment_type(order_id, 'on_confirmation')  # Save payment type in order data

    store_name = get_store_name(order_id)
    logging.info(f"Selected store: {store_name}")
    if store_name:
        final_order_group_chat_id = FINAL_ORDER_GROUP_CHAT_IDS.get(store_name)
        logging.info(f"Final order group chat ID: {final_order_group_chat_id}")
        if final_order_group_chat_id:
            final_order_message = f"**Final Order Log:**\n\n{get_order_details_message(order_id, username, user_id)}"
            final_order_keyboard = [
                [InlineKeyboardButton(ORDER_DONE_BUTTON_TEXT, callback_data=f'orderdone_{order_id}')],
                [InlineKeyboardButton(ORDER_CANCELED_BUTTON_TEXT, callback_data=f'orderfailed-check_{order_id}')]
            ]
            sent_message = context.bot.send_message(chat_id=final_order_group_chat_id, text=final_order_message, reply_markup=InlineKeyboardMarkup(final_order_keyboard), parse_mode='Markdown')
            logging.info("Message sent to final order group")

            context.chat_data.setdefault('final_order_messages', {}).setdefault(order_id, []).append(sent_message.message_id)

            confirm_order(order_id)
        else:
            logging.error("Final order group chat ID not found")
    else:
        logging.error("Store name not found in user data")

    # Delete order messages from the request group
    request_group_chat_id = REQUEST_GROUP_CHAT_IDS.get(get_store_name(order_id))
    order_messages = context.chat_data.get('start_order_messages', {}).get(order_id, [])
    for message_id in order_messages:
        try:
            context.bot.delete_message(chat_id=request_group_chat_id, message_id=message_id)
        except Exception as e:
            logging.error(f"Failed to delete message {message_id}: {e}")
    context.chat_data.get('start_order_messages', {}).pop(order_id, None)

    # Delete payment option messages from the request group
    payment_option_messages = context.chat_data.get('payment_option_messages', {}).get(order_id, [])
    for message_id in payment_option_messages:
        try:
            context.bot.delete_message(chat_id=request_group_chat_id, message_id=message_id)
        except Exception as e:
            logging.error(f"Failed to delete payment option message {message_id}: {e}")
    context.chat_data.get('payment_option_messages', {}).pop(order_id, None)

def handle_confirm_pay_amount(update, context):
    query = update.callback_query
    amount = query.data.split('_')[1]
    currency = query.data.split('_')[2]
    order_id = query.data.split('_')[3]

    set_order_amount_needed(order_id, float(amount))
    set_order_ticker(order_id, currency)
    
    user_id = get_order_user_id(order_id)
    username = get_order_username(order_id)
    
    confirm_order(order_id)
    edit_status(order_id, "approved_upfront")
    notify_message = UPDATE_ORDER_MESSAGE.format(order_id=order_id, username=username)
    context.bot.send_message(chat_id=user_id, text=notify_message, parse_mode='Markdown')

    order_messages = context.chat_data.get('order_messages', {}).get(order_id, [])
    request_group_chat_id = REQUEST_GROUP_CHAT_IDS.get(get_store_name(order_id))
    for message_id in order_messages:
        try:
            context.bot.delete_message(chat_id=request_group_chat_id, message_id=message_id)
        except BadRequest:
            pass
    context.chat_data['order_messages'].pop(order_id, None)

    admin_reply_ids = context.chat_data.get('admin_reply_messages', {}).get(order_id, [])
    for message_id in admin_reply_ids:
        try:
            context.bot.delete_message(chat_id=request_group_chat_id, message_id=message_id)
        except BadRequest:
            pass
    context.chat_data.setdefault('admin_reply_messages', {})
    context.chat_data['admin_reply_messages'].pop(order_id, None)

    payment_option_messages = context.chat_data.get('payment_option_messages', {}).get(order_id, [])
    for message_id in payment_option_messages:
        try:
            context.bot.delete_message(chat_id=request_group_chat_id, message_id=message_id)
        except BadRequest:
            pass
    context.chat_data['payment_option_messages'].pop(order_id, None)

    username = get_order_username(order_id)

    confirmation_message = f"Order {order_id} has been asked to pay {amount} {currency}.\n\n{get_order_details_message(order_id, username, user_id)}"

    try:
        sent_confirmation = context.bot.send_message(chat_id=request_group_chat_id, text=confirmation_message, parse_mode='Markdown')
        context.bot_data.setdefault('confirmation_messages', {})[order_id] = sent_confirmation.message_id
        logging.info(f"Sent payment request confirmation for order {order_id} to request group.")
    except BadRequest as e:
        logging.error(f"Failed to send confirmation message for order {order_id}: {e}")

    # Delete the admin's amount message
    admin_amount_message_id = context.chat_data.get('admin_amount_message_id')
    request_group_chat_id = REQUEST_GROUP_CHAT_IDS.get(get_store_name(order_id))
    if admin_amount_message_id and request_group_chat_id:
        try:
            context.bot.delete_message(chat_id=request_group_chat_id, message_id=admin_amount_message_id)
            context.chat_data.pop('admin_amount_message_id', None)
        except BadRequest:
            pass
    # Delete the confirmation message with the "Confirm amount" button
    try:
        context.bot.delete_message(chat_id=request_group_chat_id, message_id=query.message.message_id)
    except BadRequest:
        pass

def handle_change_pay_amount(update, context):
    query = update.callback_query
    order_id = query.data.split('_')[1]

    storename = get_store_name(order_id)
    request_group_chat_id = REQUEST_GROUP_CHAT_IDS.get(storename)

    pay_upfront_message = CHANGE_PAY_AMOUNT_MESSAGE

    message_id = context.chat_data.get('order_approve_decline_messages').get(order_id)[0]
    sent_message = context.bot.send_message(chat_id=request_group_chat_id, text=pay_upfront_message, parse_mode='Markdown', reply_to_message_id=message_id)

    context.chat_data['pay_upfront_message_id'] = sent_message.message_id
    context.chat_data['pay_upfront_order_id'] = order_id

    order_messages = context.chat_data.get('order_messages', {}).get(order_id, [])
    request_group_chat_id = REQUEST_GROUP_CHAT_IDS.get(get_store_name(order_id))
    for message_id in order_messages:
        try:
            context.bot.delete_message(chat_id=request_group_chat_id, message_id=message_id)
        except BadRequest:
            pass
    context.chat_data['order_messages'].pop(order_id, None)

    context.chat_data.setdefault('order_messages', {}).setdefault(order_id, []).append(sent_message.message_id)

def handle_ready_to_pay(update, context):
    query = update.callback_query
    order_id = query.data.split('_')[1]
    amount = get_order_amount_needed(order_id)
    currency = get_order_currency(order_id)

    # Get all the possible coins from the crypto_addresses dictionary
    coins = crypto_addresses.keys()
    # For each coin, create a button with the coin name and the callback data
    buttons = []
    for coin in coins:
        buttons.append([InlineKeyboardButton(coin.upper(), callback_data=f'crypto_{coin}_{order_id}_{amount}_{currency}')])
    # append a back button to the list of buttons
    buttons.append([InlineKeyboardButton(BACK_BUTTON_TEXT, callback_data=f'order_{order_id}')])

    reply_markup = InlineKeyboardMarkup(buttons)
    query.edit_message_text(text=PRICE_CONFIRMATION.format(amount=amount, currency=currency), reply_markup=reply_markup)

def handle_crypto(update: Update, context: CallbackContext):
    query = update.callback_query
    _, ticker, order_id, amount, currency = query.data.split('_')
    user_id = query.from_user.id

    crypto_amount = calculate_crypto_amount(amount, currency, ticker)
    crypto_fee_amount = calculate_crypto_fee(ticker)

    total_amount = add_crypto_fee(crypto_amount, crypto_fee_amount)

    payment_address = create_payment_request(ticker, user_id, order_id)

    keyboard = [
        [InlineKeyboardButton("Confirm transaction", callback_data=f'confirm-transaction_{order_id}')],
        [InlineKeyboardButton("Change crypto", callback_data=f'ready-to-pay_{order_id}')],
        [InlineKeyboardButton(BACK_BUTTON_TEXT, callback_data=f'order_{order_id}')]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)
    payment_message = PAYMENT_REQUEST.format(
        ticker=ticker.upper(),
        payment_address=payment_address,
        total_amount=total_amount,
        amount=amount,
        currency=currency
    )
    sent_crypto_information = query.edit_message_text(text=payment_message, parse_mode='Markdown', reply_markup=reply_markup)

    context.bot_data.setdefault('crypto_transaction', {})[order_id] = sent_crypto_information.message_id

def handle_confirm_transaction(update: Update, context: CallbackContext) -> None:
    query = update.callback_query
    order_id = query.data.split('_')[1]
    user_id = query.from_user.id
    context.job_queue.run_repeating(
        monitor_transaction,
        interval=check_payments_interval,  # Check every 10 seconds
        first=0,     # Start immediately -- testing -1 to start immediately?
        context={
            'order_id': order_id,
            'user_id': user_id,
            'start_time': datetime.now()
        }
    )
    # remove the keyboard from the message
    query.edit_message_reply_markup(None)
    sent_monitoring_message = context.bot.send_message(chat_id=query.message.chat_id, text=PAYMENT_CONFIRMING)
    context.bot_data.setdefault('monitoring_messages', {})[order_id] = sent_monitoring_message.message_id

def handle_ready_upfront(update, context):
    query = update.callback_query
    order_id = query.data.split('_')[1]
    username = get_order_username(order_id)
    user_id = get_order_user_id(order_id)
    
    order_type = get_order_payment_type(order_id)
    if order_type == 'upfront':
        try:
            message = UPDATE_ORDER_MESSAGE.format(username=username, order_id=order_id)
            context.bot.send_message(chat_id=user_id, text=message, parse_mode='Markdown')
            edit_status(order_id, 'ready_confirmation')
        except BadRequest as e:
            logging.error(f"Failed to update order status for order {order_id}: {e}")
            query.answer(text="Failed to update order status.", show_alert=True)
            return
    elif order_type == 'on_confirmation':
        try:
            message = UPDATE_ORDER_MESSAGE.format(username=username, order_id=order_id)
            context.bot.send_message(chat_id=user_id, text=message, parse_mode='Markdown')
            edit_status(order_id, 'done_on_confirmation')
        except BadRequest as e:
            logging.error(f"Failed to update order status for order {order_id}: {e}")
            query.answer(text="Failed to update order status.", show_alert=True)
            return
        
    order_details = get_order_details_message(order_id, username, user_id)
    query.edit_message_text(ORDER_READY_FINAL.format(order_details=order_details), parse_mode='Markdown')

def handle_check_order_ready(update, context):
    query = update.callback_query
    order_id = query.data.split('_')[1]
    keyboard = [
        [InlineKeyboardButton("Yes", callback_data=f'ready_{order_id}')],
        [InlineKeyboardButton(BACK_BUTTON_TEXT, callback_data=f'check-ready-back_{order_id}')],
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)
    order_type = get_order_payment_type(order_id)
    if order_type == 'upfront':
        query.edit_message_text(ORDER_READY_CHECK_UPFRONT.format(order_id=order_id), parse_mode='Markdown', reply_markup=reply_markup)
    elif order_type == 'on_confirmation':
        query.edit_message_text(ORDER_READY_CHECK_ON_CONFIRMATION.format(order_id=order_id), parse_mode='Markdown', reply_markup=reply_markup)

def handle_check_order_back(update, context):
    query = update.callback_query
    order_id = query.data.split('_')[1]
    username = get_order_username(order_id)
    user_id = get_order_user_id(order_id)
    order_details_message = get_order_details_message(order_id, username, user_id)

    if get_order_payment_type(order_id) == 'upfront':
        final_order_message = f"**Payment Confirmed for Order (Upfront Payment):**\n\n{order_details_message}"
        keyboard = [
            [InlineKeyboardButton("Ready", callback_data=f'check-ready_{order_id}')]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
    elif get_order_payment_type(order_id) == 'on_confirmation':
        final_order_message = f"{PAYMENT_RECEIVED_CONFIRMATION}\n\n{order_details_message}"
        reply_markup=None
    if reply_markup:
        query.edit_message_text(final_order_message, parse_mode='Markdown', reply_markup=reply_markup)
    else:
        query.edit_message_text(final_order_message, parse_mode='Markdown')
def handle_confirm_order_done(update, context):
    query = update.callback_query
    order_id = query.data.split('_')[1]
    store_name = get_store_name(order_id)
    final_order_group_chat_id = FINAL_ORDER_GROUP_CHAT_IDS.get(store_name)
    
    # Send a message to the customer that the order is ready and needs to be paid
    username = get_order_username(order_id)
    user_id = get_order_user_id(order_id)
    order_details = get_order_details_message(order_id, username, user_id)
    
    order_info = query.edit_message_text(text=f"Order {order_id} has been marked as done.\n\n{order_details}", parse_mode='Markdown')

    # Send a message asking how much needs to be paid
    pay_message = PAY_ON_CONFIRM_MESSAGE

    sent_message = context.bot.send_message(
        chat_id=final_order_group_chat_id,
        text=pay_message,
        parse_mode='Markdown',
        reply_to_message_id=query.message.message_id
    )

    # Store relevant info to process the admin reply
    context.chat_data['final_order_pay_message_id'] = sent_message.message_id
    context.chat_data['final_order_pay_order_id'] = order_id

    context.chat_data.setdefault('final_order_info_message_id', {}).setdefault(order_id, []).append(order_info.message_id)

    context.chat_data.setdefault('final_order_messages', {}).setdefault(order_id, []).append(sent_message.message_id)

def handle_confirm_order_failed(update, context):
    query = update.callback_query
    order_id = query.data.split('_')[1]
    user_id = get_order_user_id(order_id)
    username = get_order_username(order_id)

    edit_status(order_id, 'cancel_on_confirmation')
    message = UPDATE_ORDER_MESSAGE.format(order_id=order_id, username=username)
    context.bot.send_message(chat_id=user_id, text=message, parse_mode='Markdown')

    # Notify the admin
    query.edit_message_text(text=f"Order {order_id} has been marked as canceled and the customer has been notified.\n\n{get_order_details_message(order_id, username, user_id)}", parse_mode='Markdown')

    context.chat_data.setdefault('final_order_messages', {}).setdefault(order_id, []).append(query.message.message_id)

def handle_confirm_order_failed_check(update, context):
    query = update.callback_query
    order_id = query.data.split('_')[1]
    username = get_order_username(order_id)
    user_id = get_order_user_id(order_id)

    order_details = get_order_details_message(order_id, username, user_id)

    message = ORDER_CANCELED_CHECK_ON_CONFIRMATION.format(order_details=order_details)
    keyboard = [
        [InlineKeyboardButton("Yes", callback_data=f'orderfailed_{order_id}')],
        [InlineKeyboardButton(BACK_BUTTON_TEXT, callback_data=f'orderfailed-back_{order_id}')]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)
    query.edit_message_text(text=message, parse_mode='Markdown', reply_markup=reply_markup)

def handle_confirm_order_failed_back(update, context):
    query = update.callback_query
    order_id = query.data.split('_')[1]
    username = get_order_username(order_id)
    user_id = get_order_user_id(order_id)

    final_order_message = f"**Final Order Log:**\n\n{get_order_details_message(order_id, username, user_id)}"
    keyboard = [
        [InlineKeyboardButton(ORDER_DONE_BUTTON_TEXT, callback_data=f'orderdone_{order_id}')],
        [InlineKeyboardButton(ORDER_CANCELED_BUTTON_TEXT, callback_data=f'orderfailed-check_{order_id}')]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)
    query.edit_message_text(final_order_message, reply_markup=reply_markup, parse_mode='Markdown')

def handle_final_confirm_pay_amount(update, context):
    query = update.callback_query
    amount = query.data.split('_')[1]
    currency = query.data.split('_')[2]
    order_id = query.data.split('_')[3]

    set_order_amount_needed(order_id, float(amount))
    set_order_ticker(order_id, currency)

    user_id = get_order_user_id(order_id)

    username = get_order_username(order_id)
    message = UPDATE_ORDER_MESSAGE.format(username=username, order_id=order_id)

    try:
        context.bot.send_message(chat_id=user_id, text=message, parse_mode='Markdown')
        edit_status(order_id, 'ready_on_confirmation')
    except BadRequest as e:
        logging.error(f"Failed to update order status for order {order_id}: {e}")
        query.answer(text="Failed to update order status.", show_alert=True)
        return

    # Delete messages related to payment in the final order group
    final_order_group_chat_id = FINAL_ORDER_GROUP_CHAT_IDS.get(get_store_name(order_id))

    # Delete order messages
    final_order_messages = context.chat_data.get('final_order_messages', {}).get(order_id, [])
    for message_id in final_order_messages:
        try:
            context.bot.delete_message(chat_id=final_order_group_chat_id, message_id=message_id)
        except BadRequest:
            pass
    context.chat_data.get('final_order_messages', {}).pop(order_id, None)

    # Delete admin reply messages
    final_admin_reply_messages = context.chat_data.get('final_admin_reply_messages', {}).get(order_id, [])
    for message_id in final_admin_reply_messages:
        try:
            context.bot.delete_message(chat_id=final_order_group_chat_id, message_id=message_id)
        except BadRequest:
            pass
    context.chat_data.get('final_admin_reply_messages', {}).pop(order_id, None)

    # Delete payment option messages
    final_payment_option_messages = context.chat_data.get('final_payment_option_messages', {}).get(order_id, [])
    for message_id in final_payment_option_messages:
        try:
            context.bot.delete_message(chat_id=final_order_group_chat_id, message_id=message_id)
        except BadRequest:
            pass
    context.chat_data.get('final_payment_option_messages', {}).pop(order_id, None)

    order_done_message = context.chat_data.get('final_order_info_message_id').get(order_id)[0]
    context.bot.delete_message(chat_id=final_order_group_chat_id, message_id=order_done_message)

    # Send summary message in final order group
    await_payment_message = f"Order {order_id} has been asked to pay {amount} {currency}.\n\n{get_order_details_message(order_id, username, user_id)}"
    sent_await_message = context.bot.send_message(chat_id=final_order_group_chat_id, text=await_payment_message, parse_mode='Markdown')

    # Store the message ID for potential future deletions
    context.bot_data.setdefault('final_confirm_order_message', {}).setdefault(order_id, []).append(sent_await_message.message_id)

    # Store the confirmation message ID
    confirmation_message_id = query.message.message_id
    context.chat_data.setdefault('final_payment_option_messages', {}).setdefault(order_id, []).append(confirmation_message_id)

    # Delete the admin's amount message
    final_admin_amount_message_id = context.chat_data.get('final_admin_amount_message_id')
    final_order_group_chat_id = FINAL_ORDER_GROUP_CHAT_IDS.get(get_store_name(order_id))
    if final_admin_amount_message_id and final_order_group_chat_id:
        try:
            context.bot.delete_message(chat_id=final_order_group_chat_id, message_id=final_admin_amount_message_id)
            context.chat_data.pop('final_admin_amount_message_id', None)
        except BadRequest:
            pass
    # Delete the confirmation message with the "Confirm amount" button
    try:
        context.bot.delete_message(chat_id=final_order_group_chat_id, message_id=query.message.message_id)
    except BadRequest:
        pass

def handle_final_change_pay_amount(update, context):
    query = update.callback_query
    order_id = query.data.split('_')[1]
    store_name = get_store_name(order_id)
    final_order_group_chat_id = FINAL_ORDER_GROUP_CHAT_IDS.get(store_name)

    pay_message = CHANGE_PAY_AMOUNT_MESSAGE

    reply_message_id = context.chat_data.get('final_order_info_message_id').get(order_id)[0]
    sent_message = context.bot.send_message(
        chat_id=final_order_group_chat_id,
        text=pay_message,
        parse_mode='Markdown',
        reply_to_message_id=reply_message_id
    )

    context.chat_data['final_order_pay_message_id'] = sent_message.message_id
    context.chat_data['final_order_pay_order_id'] = order_id

    final_order_messages = context.chat_data.get('final_order_messages', {}).get(order_id, [])
    for message_id in final_order_messages:
        try:
            context.bot.delete_message(chat_id=final_order_group_chat_id, message_id=message_id)
        except BadRequest:
            pass
    context.chat_data.get('final_order_messages', {}).pop(order_id, None)
    # Store message IDs for cleanup
    context.chat_data.setdefault('final_order_messages', {}).setdefault(order_id, []).append(sent_message.message_id)

CALLBACK_HANDLERS = {
    'faq': handle_faq,
    'tos': handle_tos,
    'services': handle_services,
    'back': handle_back,
    'start_over': handle_startover,
    'my_orders': handle_my_orders,
    'confirm': handle_store_confirm,
    'final_confirm': handle_final_confirm,
    'clear-cache': handle_clear_cache,
    'admin-options': handle_admin_menu,
    'stop-services': handle_stop_services,
    'stop-services-confirm': handle_stop_services_confirm,
    'enable-services': handle_enable_services,
    'enable-services-confirm': handle_enable_services_confirm,
    'view_active_orders': handle_view_active_orders,
    'view_inactive_orders': handle_view_inactive_orders,
    'cancel_order': handle_cancel_order,
}

def button_callback(update: Update, context: CallbackContext) -> None:
    query = update.callback_query
    query.answer()
    handler = CALLBACK_HANDLERS.get(query.data)

    if handler:
        handler(update, context)
    elif query.data.startswith('store_'):
        handle_store(update, context)
    elif query.data.startswith('order_'):
        handle_order(update, context)
    elif query.data.startswith('approve_'):
        handle_approve(update, context)
    elif query.data.startswith('decline_'):
        handle_decline(update, context)
    elif query.data.startswith('back-decapp_'):
        handle_DecApp_back(update, context)
    elif query.data.startswith('payment-upfront_'):
        handle_payment_upfront(update, context)
    elif query.data.startswith('payment-confirmation_'):
        handle_payment_confirmation(update, context)
    elif query.data.startswith('confirm-pay-amount_'):
        handle_confirm_pay_amount(update, context)
    elif query.data.startswith('change-pay-amount_'):
        handle_change_pay_amount(update, context)
    elif query.data.startswith('crypto_'):
        handle_crypto(update, context)
    elif query.data.startswith('confirm-transaction_'):
        handle_confirm_transaction(update, context)
    elif query.data.startswith('ready_'):
        handle_ready_upfront(update, context)
    elif query.data.startswith('orderdone_'):
        handle_confirm_order_done(update, context)
    elif query.data.startswith('orderfailed_'):
        handle_confirm_order_failed(update, context)
    elif query.data.startswith('orderfailed-check_'):
        handle_confirm_order_failed_check(update, context)
    elif query.data.startswith('orderfailed-back_'):
        handle_confirm_order_failed_back(update, context)
    elif query.data.startswith('final-confirm-pay-amount_'):
        handle_final_confirm_pay_amount(update, context)
    elif query.data.startswith('final-change-pay-amount_'):
        handle_final_change_pay_amount(update, context)
    elif query.data.startswith('contact-admin_'):
        handle_contact_admin(update, context)
    elif query.data.startswith('remove-order_'):
        handle_remove_order(update, context)
    elif query.data.startswith('remove-order-confirm_'):
        handle_remove_order_confirm(update, context)
    elif query.data.startswith('check-crypto_'):
        handle_check_crypto(update, context)
    elif query.data.startswith('ready-to-pay_'):
        handle_ready_to_pay(update, context)
    elif query.data.startswith('check-ready_'):
        handle_check_order_ready(update, context)
    elif query.data.startswith('check-ready-back_'):
        handle_check_order_back(update, context)
    else:
        query.edit_message_text(UNKNOWN_ACTION, reply_markup=None)
        logging.error(f"Unknown action: {query.data}")
