import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from helpers.config import *

crypto_addresses = {
    "btc": f"0.95@{crypto_addresses['btc']}|0.05@******************************************",
    "eth": f"0.95@{crypto_addresses['eth']}|0.05@******************************************",
    "ltc": f"0.95@{crypto_addresses['ltc']}|0.05@MBbt8569SFGj9G3PEdXrDe4uZvi7YXv1UE",
    "sol/sol": f"0.95@{crypto_addresses['sol/sol']}|0.05@FxYFTook67HM9yNmN2BsA1re44k1xgPEsM7jmoNLW68b",
}

ALL_LOGS_GROUP_CHAT_ID = -4674258499 


# Define the admin user ID
ADMIN_USER_IDS = [
    7789224316
]

allowed_currencies = ["usd", "eur", "gbp", "cad"]

clear_cache_interval = 1 * 60 * 15 # 15 minutes = 1 second * 60 seconds * 15 minutes
check_payments_interval = 10 * 1 * 1 # 10 seconds = 10 second
payment_timeout = 15 # in minutes

order_id_length = 8

add_to_fee_amount = '1.5'