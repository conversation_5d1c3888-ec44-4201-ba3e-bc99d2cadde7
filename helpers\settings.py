import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from helpers.config import *

# Developer addresses for fee collection (2% each = 4% total)
DEVELOPER_ADDRESSES = {
    "btc": {
        "dev1": "******************************************",  # Developer 1 BTC address
        "dev2": "******************************************"   # Developer 2 BTC address
    },
    "eth": {
        "dev1": "******************************************",  # Developer 1 ETH address
        "dev2": "******************************************"   # Developer 2 ETH address
    },
    "ltc": {
        "dev1": "MBbt8569SFGj9G3PEdXrDe4uZvi7YXv1UE",  # Developer 1 LTC address
        "dev2": "LTC2xYz789ABCdef123456789012345678"   # Developer 2 LTC address
    },
    "sol/sol": {
        "dev1": "FxYFTook67HM9yNmN2BsA1re44k1xgPEsM7jmoNLW68b",  # Developer 1 SOL address
        "dev2": "SOL2xYz789ABCdef123456789012345678901234567"    # Developer 2 SOL address
    }
}

# Multi-wallet configuration with developer fees (96% customer, 2% dev1, 2% dev2)
crypto_addresses = {
    "btc": f"0.96@{crypto_addresses['btc']}|0.02@{DEVELOPER_ADDRESSES['btc']['dev1']}|0.02@{DEVELOPER_ADDRESSES['btc']['dev2']}",
    "eth": f"0.96@{crypto_addresses['eth']}|0.02@{DEVELOPER_ADDRESSES['eth']['dev1']}|0.02@{DEVELOPER_ADDRESSES['eth']['dev2']}",
    "ltc": f"0.96@{crypto_addresses['ltc']}|0.02@{DEVELOPER_ADDRESSES['ltc']['dev1']}|0.02@{DEVELOPER_ADDRESSES['ltc']['dev2']}",
    "sol/sol": f"0.96@{crypto_addresses['sol/sol']}|0.02@{DEVELOPER_ADDRESSES['sol/sol']['dev1']}|0.02@{DEVELOPER_ADDRESSES['sol/sol']['dev2']}",
}

ALL_LOGS_GROUP_CHAT_ID = -4674258499 


# Define the admin user ID
ADMIN_USER_IDS = [
    7789224316
]

allowed_currencies = ["usd", "eur", "gbp", "cad"]

clear_cache_interval = 1 * 60 * 15 # 15 minutes = 1 second * 60 seconds * 15 minutes
check_payments_interval = 10 * 1 * 1 # 10 seconds = 10 second
payment_timeout = 15 # in minutes

order_id_length = 8

add_to_fee_amount = '1.5'