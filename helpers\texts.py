import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from helpers.config import *
from helpers.config import WELCOME_MESSAGE, CONTACT_ADMIN, FAQ_TEXT, TOS_TEXT, SERVICES_TEXT

#---------------------------------------------------------------------------------------------------------------

# Payment messages
PRICE_CONFIRMATION = "Please pay the following amount: {amount} {currency}.\n\nSelect the cryptocurrency you want to pay with from below."

PAYMENT_REQUEST = """Please use the following payment details to complete your order:\n\n📍Payment address:\n`{payment_address}`\n\n💰Amount:\n`{total_amount}` {ticker} ({amount} {currency})\n\n🗒️Please make sure to include enough for the transaction fees.\n🗒️Once the payment is confirmed, we will proceed with your order.\n\n❗Make sure to only click on Confirm transaction, after you send the payment!"""
PAYMENT_REQUEST_REMAINING = """You did not send enough, make sure to send the remaining amount to the following payment details:\n\n📍Payment address:\n`{payment_address}`\n\n💰Amount:\n`{total_amount}` {ticker} ({amount} {currency})\n\n🗒️Double check if after transaction fees you are sending enough.\n🗒️Once the payment is confirmed, we will proceed with your order.\n\n❗Make sure to only click on Confirm transaction, after you send the payment!"""

PAYMENT_CONFIRMING = "🔄 Monitoring your transaction. You will be notified once it is confirmed."
TRANSACTION_TIMEOUT = "⚠️ Transaction verification timed out after 15 minutes. Please contact support for assistance."
TRANSACTION_CONFIRMED = "✅ Transaction confirmed! Your order is being processed."

# Admin & error messages
INVALID_AMOUNT_FORMAT = "Invalid format. Please reply with the amount followed by the currency (e.g., 100usd, 200eur, 300gbp)."
ORDER_NOT_FOUND = "Order ID not found. Please try again."
PAY_UPFRONT_MESSAGE = "How much does the customer need to pay upfront?\n\nPlease reply with the amount and currency (e.g., 100usd, 200eur, 300gbp, 400cad)."
CHANGE_PAY_AMOUNT_MESSAGE = "How much does the customer need to pay?\n\nPlease reply with the amount and currency (e.g., 100usd, 200eur, 300gbp, 400cad)."


BACK_BUTTON_TEXT = "🔙"

UNKNOWN_ACTION = "Unknown action."

ORDER_DONE_BUTTON_TEXT = "Order done"
ORDER_CANCELED_BUTTON_TEXT = "Order canceled"

MYORDER_MESSAGE_TEXT = "Here are your pending orders:"
NO_ORDERS_MESSAGE_TEXT = "You have no pending orders."

NEXT_INFORMATION_PROMPT = "Please provide the following information:"

OVERVIEW_MESSAGE_TEXT = "Here is the information you provided:"

CONFIRM_PAYMENT_AMOUNT_MESSAGE_TEXT = "Please confirm the payment amount:"
#----------------------------------------------------------------------------------

# 1. Welcome message
# WELCOME_MESSAGE = "Welcome to our bot! Choose an option below:"

'''# 2a. FAQ message
FAQ_TEXT = "Here are the frequently asked questions:\nQ: What is this bot for?\nA: This bot is for testing purposes only."

# 2b. Terms of Service message
TOS_TEXT = "By using this bot, you agree to the following terms:\n- You will not spam the bot.\n- You will not share inappropriate content."

# 2c. Services message
SERVICES_TEXT = "Hi there!\n\nPlease choose a service below:"'''

# 2d. 0rders tab
ORDERS_TEXT = "Here are all your orders with their status:"

# 3. Store messages (function above)
STORE_1_TEXT

# 4. Order sent to service team message
ORDER_SUCCESS = "Your order has been sent to the service team."

#----------------------------------------------------------------------------------

#Once the client receives an update on their order, they will receive this message:
UPDATE_ORDER_MESSAGE = "Hi {username},\n\nThere’s an update on your order 📦 {order_id}.\n\nCheck your order status via:\n/myorders or `/status {order_id}`."

#Once the client checks the status of their order, they will receive this message:
ORDER_STATUS_MESSAGE = "Status: {status}\n\n{order_details}"

#----------------------------------------------------------------------------------

#STATUS MESSAGES:

#Declined order message
ORDER_DECLINED_MESSAGE = "Unfortunately, your order has been declined. Please contact the admin for further details or assistance."

#Approved on confirmation message
ORDER_APPROVED_ON_CONFIRMATION_MESSAGE = "Good news! Your order has been approved. Payment will be required upon confirmation of your order. No action is needed right now simply wait for the next status update."

#Order ready on confirmation message
ORDER_READY_ON_CONFIRMATION_MESSAGE = "Your order has been confirmed and now requires payment. Please proceed with the payment of {amount} {currency}. Make sure you pay on-time to avoid any problems."

#Order cancel on confirmation message
ORDER_CANCEL_ON_CONFIRMATION_MESSAGE = "Your order has been cancelled. Please contact the admin for further details or assistance."

#Order done on confirmation message
ORDER_DONE_ON_CONFIRMATION_MESSAGE = "Your order has been completed successfully. Please contact the admin for further details or assistance."

#Approved upfront payment message
ORDER_APPROVED_UPFRONT_PAYMENT_MESSAGE = "Great news! Your order has been approved. However, payment of {amount} {currency} is required upfront to proceed. If you choose not to pay upfront, the order cannot be processed."

#Order ready upfront payment message
ORDER_READY_UPFRONT_PAYMENT_MESSAGE = "Your order has been confirmed and is now done. Because you have paid upfront, no further action is needed. Thank you for using our services."

ORDER_NO_UPDATE_MESSAGE = "There is no new update yet for your order. Please check the FAQ or contact the admin for further assistance if you have any questions."

ORDER_UPFRONT_PAID_STATUS = "We have received your payment, your order is being processed. Just simply wait for the next status update."

STATUS_MESSAGES = {
    "no_update": {
        "message": ORDER_NO_UPDATE_MESSAGE,
        "buttons": [
            {"text": "Contact admin", "callback_data": "contact-admin_{order_id}"},
            {"text": BACK_BUTTON_TEXT, "callback_data": "my_orders"}
        ]
    },
    "declined": {
        "message": ORDER_DECLINED_MESSAGE,
        "buttons": [
            {"text": "Contact admin", "callback_data": "contact-admin_{order_id}"},
            {"text": "Okay no problem, remove this order out of the database.", "callback_data": "remove-order_{order_id}"},
            {"text": BACK_BUTTON_TEXT, "callback_data": "my_orders"}
        ]
    },
    "approved_on_confirmation": {
        "message": ORDER_APPROVED_ON_CONFIRMATION_MESSAGE,
        "buttons": [
            {"text": "Contact admin", "callback_data": "contact-admin_{order_id}"},
            {"text": BACK_BUTTON_TEXT, "callback_data": "my_orders"}
        ]
    },
    "ready_on_confirmation": {
        "message": ORDER_READY_ON_CONFIRMATION_MESSAGE,
        "buttons": [
            {"text": "Contact admin", "callback_data": "contact-admin_{order_id}"},
            {"text": "Check cryptocurrencies", "callback_data": "check-crypto_{order_id}"},
            {"text": "I'm ready to pay", "callback_data": "ready-to-pay_{order_id}"},
            {"text": BACK_BUTTON_TEXT, "callback_data": "my_orders"}
        ]
    },
    "cancel_on_confirmation": {
        "message": ORDER_CANCEL_ON_CONFIRMATION_MESSAGE,
        "buttons": [
            {"text": "Contact admin", "callback_data": "contact-admin_{order_id}"},
            {"text": "Okay no problem, remove this order out of the database.", "callback_data": "remove-order_{order_id}"},
            {"text": BACK_BUTTON_TEXT, "callback_data": "my_orders"}
        ]
    },
    "done_on_confirmation": {
        "message": ORDER_DONE_ON_CONFIRMATION_MESSAGE,
        "buttons": [
            {"text": "Contact admin", "callback_data": "contact-admin_{order_id}"},
            {"text": "Okay no problem, remove this order out of the database.", "callback_data": "remove-order_{order_id}"},
            {"text": BACK_BUTTON_TEXT, "callback_data": "my_orders"}
        ]
    },
    "approved_upfront": {
        "message": ORDER_APPROVED_UPFRONT_PAYMENT_MESSAGE,
        "buttons": [
            {"text": "Contact admin", "callback_data": "contact-admin_{order_id}"},
            {"text": "Check cryptocurrencies", "callback_data": "check-crypto_{order_id}"},
            {"text": "I'm ready to pay", "callback_data": "ready-to-pay_{order_id}"},
            {"text": BACK_BUTTON_TEXT, "callback_data": "my_orders"}
        ]
    },
    "ready_confirmation": {
        "message": ORDER_READY_UPFRONT_PAYMENT_MESSAGE,
        "buttons": [
            {"text": "Contact admin", "callback_data": "contact-admin_{order_id}"},
            {"text": "Thank you! Remove this order from the database.", "callback_data": "remove-order_{order_id}"},
            {"text": BACK_BUTTON_TEXT, "callback_data": "my_orders"}
        ]
    },
    "upfront_paid": {
        "message": ORDER_UPFRONT_PAID_STATUS, 
        "buttons": [
            {"text": "Contact admin", "callback_data": "contact-admin_{order_id}"},
            {"text": BACK_BUTTON_TEXT, "callback_data": "my_orders"}
        ] 
    }
}

#CONTACT_ADMIN = "@username"
REMOVE_ORDER = "❗️ Are you sure you want to remove this order? This action CAN NOT be undone. ❗️\n\nThis will remove the order from the database and you will NOT be able to see it anymore."
REMOVE_ORDER_CONFIRM = "You removed the order successfully."
CHECK_CRYPTO = """💰 Hi there! We accept the following cryptocurrencies:

🔸 Bitcoin (BTC)
🔹 Ethereum (ETH)
🔸 Litecoin (LTC)
🔹 Solana (SOL)

Whenever you are ready to pay, please select "I'm ready to pay" and proceed with the payment. 🚀"""

ORDER_STATUS = "Order status: {status}"

ORDER_APPROVED = "Please choose when the customer should pay.\n\n{order_details}"
ORDER_DECLINED = "Order declined placeholder"
PAY_ON_CONFIRM_MESSAGE = "How much does the customer need to pay?\n\nPlease reply with the amount and currency (e.g., 100usd, 200eur, 300gbp, 400cad)."
ORDER_CONFIRMED_USER = "✅ Transaction confirmed! Thank you for using our services!"
ORDER_READY = "Order ready placeholder"
ORDER_READY_FINAL = "This order has been marked as 100% completed.\n\n{order_details}"

ORDER_UPFRONT_PAID = "✅ Transaction confirmed! Your order is being processed."
ORDER_CONFIRMING_PAID = "✅ Transaction confirmed! You're order has been finished, you can delete the order with /myorders"

ORDER_READY_CHECK_UPFRONT = """This order has been paid upfront.\n\nAre you sure you want to mark this order as 100% completed?"""
ORDER_READY_CHECK_ON_CONFIRMATION = """This order has been paid on confirmation.\n\nAre you sure you want to mark this order as 100% completed?"""

ORDER_CANCELED_CHECK_ON_CONFIRMATION = "Are you sure you want to mark this order as canceled? \n\n{order_details}"

ORDER_HISTORY_HEADER = "📚 *Order History*\n\n"
NO_INACTIVE_ORDERS = "📭 No completed/canceled orders found in history"

ORDER_INFO_HELP = "❌ Usage: /order_info <order_id> <user_id>\nExample: /order_info ABC123 456789"
ORDER_NOT_FOUND = "❌ Order not found for specified user"

PAYMENT_RECEIVED_CONFIRMATION = "Payment Confirmed for Order (Payment on Confirmation):"