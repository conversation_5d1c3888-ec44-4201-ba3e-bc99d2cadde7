# Bot features needed for sell
- [ ] Bot will be hosted by admins themselves, we have nothing to do with hosting it.
- [ ] We sell the bot for a one-time price.
- [ ] The code will be encrypted using pyarmor.
- [ ] The only file customers will have real access to (and that won't be encrypted) is config.py

## Performance improvements
- The bot had issues with locking everything when certain functions were being executed for other users
- The bot needs to be able to handle up to 500 concurrent users interacting with the bot at the same time without it hanging for some

## Dev control
- Each bot should also have some features we are able to customize through a predefined "admin" bot which can do things like terminate access for when unauthorized use is found
- Each bot should be activated before it can actually be used by customers through a code they receive from us.


## What is config.py?
Config.py is a file with all the settings and information customers need to adjust themselves
For example:
- Wallet addresses (fe customers puts his own btc wallet address to receive them) => if the customer leaves any empty, that crypto won't be used/usable.
- Texts (fe the welcome message)
- The log telegram group ids
- ...

## Backend for developers
- [ ] Multi wallet via cryptapi. The devs (we, creators of this bot) receive 2% of each transaction (4% in total for the 2 devs).
- [ ] Secret log. The developers have their own telegram group where they receive all logs being sent, each customer/bot would get their own topic.
- [ ] These topic chat ids should not be stored in the customers database but instead be gathered from somewhere online through another database or public way to download/retrieve the chat id.

<!-- ### Secret log info
- Misschien iets maken dat de groupid word opgehaald van een prive link van ons, dat we onze log chat altijd kunnen aanpassen.
- Bijvoorbeeld: wij verwijderen perongeluk die log groep => dan krijgen wij nooit meer logs.
- Dus: Maak mss iets dat we deze log group id via een apart ding kunnen aanpassen zonder problemen. -->

## Readme
Readme met volledige uitleg over hoe de admin de bot moet opzetten en aanpassen.
### Secties in readme:
1. Intro (This bot handles all your orders...)
2. Requirements for setup (VPS, code-editor, ...)
3. VPS (We recommend that you buy a VPS via hostinger.com via this link: ...)
4. The setup (Do the following commands in your vps terminal ...)
5. All done! (Congratulions! Your bot is now running! If you have any questions, feel free tot contact ...)
