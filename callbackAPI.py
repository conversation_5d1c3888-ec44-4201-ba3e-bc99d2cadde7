from flask import Flask, request, jsonify
import sqlite3
import json
from cryptography.hazmat.primitives import serialization, hashes
from cryptography.hazmat.primitives.asymmetric import padding
from cryptography.hazmat.backends import default_backend
import base64

app = Flask(__name__)

pub_str = "-----BEGIN PUBLIC KEY-----\nMIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC3FT0Ym8b3myVxhQW7ESuuu6lo\ndGAsUJs4fq+Ey//jm27jQ7HHHDmP1YJO7XE7Jf/0DTEJgcw4EZhJFVwsk6d3+4fy\nBsn0tKeyGMiaE6cVkX0cy6Y85o8zgc/CwZKc0uw6d5siAo++xl2zl+RGMXCELQVE\nox7pp208zTvown577wIDAQAB\n-----END PUBLIC KEY-----";

def init_db():
    conn = sqlite3.connect('callback.db')
    cursor = conn.cursor()
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS Payment (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            userid TEXT,
            orderid TEXT,
            txid_in TEXT,
            txid_out TEXT,
            confirmations INTEGER,
            value_coin REAL,
            value_coin_convert TEXT,
            coin TEXT,
            price REAL,
            pending INTEGER,
            value_forwarded_coin REAL,
            value_forwarded_coin_convert TEXT,
            fee_coin REAL
        )
    ''')
    conn.commit()
    conn.close()

@app.route('/callback', methods=['POST'])
def callback():
    userid = request.args.get('userid')
    orderid = request.args.get('orderid')
    data = request.json
    pending = data.get('pending')

    if pending == 1:
        txid_in = data.get('txid_in')
        confirmations = data.get('confirmations')
        value_coin = data.get('value_coin')
        value_coin_convert = data.get('value_coin_convert')
        coin = data.get('coin')
        price = data.get('price')
        txid_out = ''
        value_forwarded_coin = ''
        value_forwarded_coin_convert = ''
        fee_coin = ''

        conn = sqlite3.connect('callback.db')
        cursor = conn.cursor()
        # Check if orderid already exists
        try:
            cursor.execute('SELECT value_coin_convert FROM Payment WHERE userid = ? AND orderid = ?', (userid, orderid))
            result = cursor.fetchone()
        except:
            print(f"UserID and OrderID not found in database: {userid}, {orderid}\nCreating new record.")
            result = None
        if result:
            # Order exists, add new price to current price and set pending to 1
            existing_value_coin_convert_str = result[0]
            existing_value_coin_convert = json.loads(existing_value_coin_convert_str)
            new_value_coin_convert = data.get('value_coin_convert', {})
            for currency, amount in new_value_coin_convert.items():
                existing_value_coin_convert[currency] = str(float(existing_value_coin_convert.get(currency, 0)) + float(amount))
            cursor.execute('''
                UPDATE Payment SET 
                    value_coin_convert = ?
                WHERE userid = ? AND orderid = ?
            ''', (json.dumps(existing_value_coin_convert), userid, orderid))
        else:
            # Order does not exist, insert new record
            value_coin_convert_str = json.dumps(value_coin_convert)
            cursor.execute('''
                INSERT INTO Payment (
                    userid, orderid, txid_in, txid_out, confirmations, value_coin, 
                    value_coin_convert, coin, price, pending, value_forwarded_coin, 
                    value_forwarded_coin_convert, fee_coin
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (userid, orderid, txid_in, txid_out, confirmations, value_coin, 
                value_coin_convert_str, coin, price, pending, value_forwarded_coin,
                value_forwarded_coin_convert, fee_coin))
        conn.commit()
        conn.close()
    elif pending == 0:
        txid_in = data.get('txid_in')
        txid_out = data.get('txid_out')
        confirmations = data.get('confirmations')
        value_coin = data.get('value_coin')
        value_coin_convert = data.get('value_coin_convert')
        value_coin_convert_str_confirmed = json.dumps(value_coin_convert)
        coin = data.get('coin')
        price = data.get('price')
        value_forwarded_coin = data.get('value_forwarded_coin')
        value_forwarded_coin_convert = data.get('value_forwarded_coin_convert')
        value_forwarded_coin_convert_str_confirmed = json.dumps(value_forwarded_coin_convert)
        fee_coin = data.get('fee_coin')
        conn = sqlite3.connect('callback.db')
        cursor = conn.cursor()
        cursor.execute('''
            UPDATE Payment SET 
                txid_in = ?, 
                txid_out = ?, 
                confirmations = ?, 
                value_coin = ?, 
                coin = ?, 
                price = ?, 
                pending = ?, 
                value_forwarded_coin = ?, 
                value_forwarded_coin_convert = ?, 
                fee_coin = ?
            WHERE userid = ? AND orderid = ?
        ''', (txid_in, txid_out, confirmations, value_coin, 
            coin, price, pending, value_forwarded_coin, value_forwarded_coin_convert_str_confirmed, 
            fee_coin, userid, orderid))
        conn.commit()
        conn.close()

    print(f"Callback received: {data}, UserID: {userid}, OrderID: {orderid}")
    return jsonify({"verified": True})

if __name__ == '__main__':
    init_db()
    app.run(host='0.0.0.0', port=5000)