from datetime import datetime
import sqlite3
import json

def init_db():
    conn = sqlite3.connect('bot.db')
    cursor = conn.cursor()
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS orders (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT,
            user_id INTEGER,
            store_name TEXT,
            info_responses TEXT,
            order_id TEXT,
            status TEXT DEFAULT 'Pending',
            payment_type TEXT DEFAULT 'upfront',
            status_message TEXT DEFAULT 'no_update'
        )
    ''')
    # Add the order_id and status columns if they don't exist
    cursor.execute('''
        PRAGMA table_info(orders)
    ''')
    columns = [column[1] for column in cursor.fetchall()]
    if 'order_id' not in columns:
        cursor.execute('''
            ALTER TABLE orders ADD COLUMN order_id TEXT
        ''')
    if 'status' not in columns:
        cursor.execute('''
            ALTER TABLE orders ADD COLUMN status TEXT DEFAULT 'Pending'
        ''')
    if 'amount_needed' not in columns:
        cursor.execute("ALTER TABLE orders ADD COLUMN amount_needed REAL DEFAULT 0")
    if 'ticker' not in columns:
        cursor.execute("ALTER TABLE orders ADD COLUMN ticker REAL DEFAULT 0")
    conn.commit()
    conn.close()

def save_order(username: str, user_id: str, store_name: str, info_responses: str, order_id: str) -> None:
    conn = sqlite3.connect('bot.db')
    cursor = conn.cursor()
    cursor.execute('''
        INSERT INTO orders (username, user_id, store_name, info_responses, order_id)
        VALUES (?, ?, ?, ?, ?)
    ''', (username, user_id, store_name, info_responses, order_id))
    conn.commit()
    conn.close()

def get_order_details(order_id: str) -> str:
    conn = sqlite3.connect('bot.db')
    cursor = conn.cursor()
    cursor.execute('''
        SELECT info_responses FROM orders WHERE order_id = ?
    ''', (order_id,))
    result = cursor.fetchone()
    conn.close()
    if result:
        info_responses = json.loads(result[0])
        formatted_details = "\n".join([f"{key}: {value}" for key, value in info_responses.items()])
        return formatted_details
    return "Order details not found."

def confirm_order(order_id: str):
    conn = sqlite3.connect('bot.db')
    cursor = conn.cursor()
    cursor.execute('''
        UPDATE orders SET status = 'Confirmed' WHERE order_id = ?
    ''', (order_id,))
    conn.commit()
    conn.close()

def get_confirmed_orders(user_id: str) -> list:
    conn = sqlite3.connect('bot.db')
    cursor = conn.cursor()
    cursor.execute('''
        SELECT order_id, store_name, info_responses FROM orders WHERE user_id = ? AND status = 'Confirmed'
    ''', (user_id,))
    results = cursor.fetchall()
    conn.close()
    orders = []
    for result in results:
        orders.append({
            'order_id': result[0],
            'store_name': result[1],
            'info_responses': json.loads(result[2])
        })
    return orders

def get_store_name(order_id: str) -> str:
    conn = sqlite3.connect('bot.db')
    cursor = conn.cursor()
    cursor.execute('''
        SELECT store_name FROM orders WHERE order_id = ?
    ''', (order_id,))
    result = cursor.fetchone()
    conn.close()
    if result:
        return result[0]
    return "Unknown Store"

def delete_order(order_id: str):
    conn = sqlite3.connect('bot.db')
    cursor = conn.cursor()
    cursor.execute('DELETE FROM orders WHERE order_id = ?', (order_id,))
    conn.commit()
    conn.close()

def get_order_user_id(order_id: str) -> str:
    conn = sqlite3.connect('bot.db')
    cursor = conn.cursor()
    cursor.execute('''
        SELECT user_id FROM orders WHERE order_id = ?
    ''', (order_id,))
    result = cursor.fetchone()
    conn.close()
    if result:
        return result[0]
    return "Unknown User"

def get_order_username(order_id: str) -> str:
    conn = sqlite3.connect('bot.db')
    cursor = conn.cursor()
    cursor.execute('''
        SELECT username FROM orders WHERE order_id = ?
    ''', (order_id,))
    result = cursor.fetchone()
    conn.close()
    if result:
        return result[0]
    return "Unknown User"

def check_transaction_status(order_id: str) -> int:
    """Check transaction status (0=confirmed, 1=pending)"""
    conn = sqlite3.connect('callback.db')
    cursor = conn.cursor()
    cursor.execute('SELECT pending FROM Payment WHERE orderid = ?', (order_id,))
    result = cursor.fetchone()
    conn.close()
    return result[0] if result else None

def update_order_payment_type(order_id: str, payment_type: str):
    conn = sqlite3.connect('bot.db')
    cursor = conn.cursor()
    cursor.execute('UPDATE orders SET payment_type = ? WHERE order_id = ?', (payment_type, order_id))
    conn.commit()
    conn.close()

def get_order_payment_type(order_id: str) -> str:
    conn = sqlite3.connect('bot.db')
    cursor = conn.cursor()
    cursor.execute('SELECT payment_type FROM orders WHERE order_id = ?', (order_id,))
    result = cursor.fetchone()
    conn.close()
    return result[0] if result else None

def set_order_amount_needed(order_id: str, amount: float):
    conn = sqlite3.connect('bot.db')
    cursor = conn.cursor()
    cursor.execute("UPDATE orders SET amount_needed = ? WHERE order_id = ?", (amount, order_id))
    conn.commit()
    conn.close()

def set_order_ticker(order_id: str, ticker: str):
    conn = sqlite3.connect('bot.db')
    cursor = conn.cursor()
    cursor.execute("UPDATE orders SET ticker = ? WHERE order_id = ?", (ticker, order_id))
    conn.commit()
    conn.close()

def get_order_amount_needed(order_id: str) -> float:
    conn = sqlite3.connect('bot.db')
    cursor = conn.cursor()
    cursor.execute("SELECT amount_needed FROM orders WHERE order_id = ?", (order_id,))
    result = cursor.fetchone()
    conn.close()
    return result[0] if result else 0

def get_order_currency(order_id: str) -> str:
    conn = sqlite3.connect('bot.db')
    cursor = conn.cursor()
    cursor.execute("SELECT ticker FROM orders WHERE order_id = ?", (order_id,))
    result = cursor.fetchone()
    conn.close()
    return result[0] if result else 0

def get_order_amount_paid_fiat(order_id: str):
    conn = sqlite3.connect('callback.db')
    cursor = conn.cursor()
    cursor.execute("SELECT value_coin_convert FROM Payment WHERE orderid = ?", (order_id,))
    result = cursor.fetchone()
    conn.close()
    return result[0] if result else 0

def get_payment_coin(order_id: str):
    conn = sqlite3.connect('callback.db')
    cursor = conn.cursor()
    cursor.execute("SELECT coin FROM Payment WHERE orderid = ?", (order_id,))
    result = cursor.fetchone()
    conn.close()
    return result[0] if result else 0

def edit_status(order_id: str, status: str):
    conn = sqlite3.connect('bot.db')
    cursor = conn.cursor()
    cursor.execute("UPDATE Orders SET status_message = ? WHERE order_id = ?", (status, order_id))
    conn.commit()
    conn.close()

def get_status(order_id: str) -> str:
    conn = sqlite3.connect('bot.db')
    cursor = conn.cursor()
    cursor.execute("SELECT status_message FROM Orders WHERE order_id = ?", (order_id,))
    result = cursor.fetchone()
    conn.close()
    return result[0] if result else ""

def user_has_order(user_id: str, order_id: str) -> bool:
    conn = sqlite3.connect('bot.db')
    cursor = conn.cursor()
    cursor.execute('''
        SELECT 1 FROM orders WHERE order_id = ? AND user_id = ?
    ''', (order_id, user_id))
    result = cursor.fetchone()
    conn.close()
    return bool(result)

def get_active_orders():
    excluded_statuses = ['declined', 'cancel_on_confirmation', 'done_on_confirmation', 'ready_confirmation']
    conn = sqlite3.connect('bot.db')
    cursor = conn.cursor()
    cursor.execute(f'''
        SELECT order_id, user_id, username, store_name, status_message 
        FROM orders 
        WHERE status_message NOT IN ({','.join(['?']*len(excluded_statuses))})
    ''', excluded_statuses)
    
    orders = []
    for row in cursor.fetchall():
        orders.append({
            'order_id': row[0],
            'user_id': row[1],
            'username': row[2],
            'store_name': row[3],
            'status': row[4]
        })
    conn.close()
    return orders

def get_payment_status(order_id, user_id):
    conn = sqlite3.connect('callback.db')
    cursor = conn.cursor()
    try:
        cursor.execute('''
            SELECT pending, confirmations 
            FROM Payment 
            WHERE orderid = ? AND userid = ?
            ORDER BY id DESC 
            LIMIT 1
        ''', (order_id, user_id))
        result = cursor.fetchone()
        if result:
            return "🟢 Confirmed" if result[0] == 0 else f"🟠 Pending ({result[1]}/6 confirmations)"
        return "⚪️ No payment data"
    except sqlite3.OperationalError:
        return "⚪️ No payment data"
    finally:
        conn.close()

def get_inactive_orders():
    target_statuses = ['declined', 'cancel_on_confirmation', 'done_on_confirmation', 'ready_confirmation']
    conn = sqlite3.connect('bot.db')
    cursor = conn.cursor()
    cursor.execute(f'''
        SELECT order_id, user_id, username, store_name, status_message, MAX(id) as last_updated 
        FROM orders 
        WHERE status_message IN ({','.join(['?']*len(target_statuses))})
        GROUP BY order_id
        ORDER BY last_updated DESC
    ''', target_statuses)
    
    orders = []
    for row in cursor.fetchall():
        orders.append({
            'order_id': row[0],
            'user_id': row[1],
            'username': row[2],
            'store_name': row[3],
            'status': row[4],
            'last_updated': datetime.datetime.fromtimestamp(row[5]).strftime('%Y-%m-%d %H:%M')
        })
    conn.close()
    return orders

def get_full_order_details(order_id: str, user_id: str) -> str:
    # Get order data from bot.db
    conn = sqlite3.connect('bot.db')
    cursor = conn.cursor()
    cursor.execute('''
        SELECT username, store_name, info_responses, status_message, payment_type 
        FROM orders 
        WHERE order_id = ? AND user_id = ?
    ''', (order_id, user_id))
    order_data = cursor.fetchone()
    conn.close()

    if not order_data:
        return "❌ Order not found"

    # Format order information
    username, store_name, info_responses, status, payment_type = order_data
    try:
        info_dict = json.loads(info_responses)
        formatted_details = "\n".join([f"\t *{k}*: {v}" for k, v in info_dict.items()])
    except:
        formatted_details = "⚠️ Could not parse order details"

    # Get payment data from callback.db
    payment_data = get_payment_details(order_id, user_id)
    
    message = (
        f"📋 *Order Details* (`{order_id}`)\n"
        f"👤 User: @{username} (`{user_id}`)\n"
        f"🏬 Store: {store_name}\n"
        f"📮 Status: {status.replace('_', ' ').title()}\n"
        f"💳 Payment Type: {payment_type.replace('_', ' ').title()}\n\n"
        f"📝 *Order Information*\n{formatted_details}\n\n"
        f"💸 *Payment Details*\n{payment_data}"
    )
    
    return message

def get_payment_details(order_id: str, user_id: str) -> str:
    conn = sqlite3.connect('callback.db')
    cursor = conn.cursor()
    try:
        # First check if payment table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='Payment'")
        if not cursor.fetchone():
            return "No payment information found (no payment table)"

        cursor.execute('''
            SELECT coin, value_coin, value_coin_convert, confirmations, pending,
                   txid_in, txid_out, fee_coin
            FROM Payment 
            WHERE orderid = ? AND userid = ?
            ORDER BY id DESC 
            LIMIT 1
        ''', (order_id, user_id))
        payment = cursor.fetchone()
        
        if not payment:
            return "No payment information found"

        (coin, value_coin, value_convert, confirmations, pending,
         txid_in, txid_out, fee) = payment
        
        try:
            conversions = json.loads(value_convert)
            # Filter only USD and EUR conversions
            conversions_filtered = {k: v for k, v in conversions.items() if k.lower() in ['usd', 'eur']}
            conversions_formatted = "\n".join([f"\t ‣ {k}: {v}" for k, v in conversions_filtered.items()])
        except Exception as e:
            return f"Could not parse conversions: {str(e)}"

        return (
            f"• Cryptocurrency: {coin.upper()}\n"
            f"• Amount: {value_coin} {coin.upper()}\n"
            f"• Fiat Conversions:\n{conversions_formatted or 'None'}\n"
            f"• Confirmations: {confirmations}/6\n"
            f"• Status: {'Pending' if pending else 'Confirmed'}\n"
            f"• TXID In: `{txid_in}`\n"
            f"• TXID Out: `{txid_out or 'N/A'}`\n"
            f"• Fees: {fee or 0} {coin.upper()}"
        )
        
    except sqlite3.OperationalError as e:
        return f"Payment database error: {str(e)}"
    except Exception as e:
        return f"Unexpected error: {str(e)}"
    finally:
        conn.close()