# Import from python modules
from telegram import *
from telegram.ext import *
from telegram.error import *
from datetime import datetime, timedelta
from decimal import Decimal
import requests
import logging
import random
import string
import re
import time

# Import from own modules
from helpers.texts import *
from helpers.data import *
from helpers.settings import *
from helpers.config import CALLBACK_BASE_URL  # Import the callback base URL

amount_currency_pattern = re.compile(
    r'^\d+({})$'.format('|'.join(allowed_currencies)),
    re.IGNORECASE
)
sent_all_info = False

requests_cache = {}

def clear_cached_payments(user_id=None):
    if user_id is None:
        requests_cache.clear()
    else:
        keys_to_remove = []
        for key in requests_cache:
            if user_id in key:
                keys_to_remove.append(key)
        for key in keys_to_remove:
            del requests_cache[key]

    # Clear calculate_crypto_amount and calculate_crypto_fee caches
    keys_to_remove = [key for key in requests_cache if key[0] in ("calculate_crypto_amount", "calculate_crypto_fee")]
    for key in keys_to_remove:
        del requests_cache[key]

def create_payment_request(ticker, user_id, order_id):
    cache_key = (order_id, user_id, ticker)
    if cache_key in requests_cache:
        return requests_cache[cache_key]
    url = f"https://api.cryptapi.io/{ticker}/create/"
    query = {
        "callback": f"{CALLBACK_BASE_URL}?userid={user_id}&orderid={order_id}",  # Use the configurable callback URL
        "address": crypto_addresses[ticker],
        "pending": "1",
        "confirmations": "6",
        "json": "1",
        "convert": "1"
    }
    print(f"Callback URL: {query['callback']}")  # Print the callback URL for testing

    response = requests.get(url, params=query)
    data = response.json()
    address_in = data["address_in"]
    requests_cache[cache_key] = address_in
    return address_in

def calculate_crypto_amount(amount, currency, ticker):
    cache_key = ("calculate_crypto_amount", amount, currency, ticker)
    if cache_key in requests_cache:
        return requests_cache[cache_key]
    url = f"https://api.cryptapi.io/{ticker}/convert/"
    query = {
        "value": amount,
        "from": currency,
    }
    response = requests.get(url, params=query)
    data = response.json()
    requests_cache[cache_key] = data["value_coin"]
    return data["value_coin"]

def calculate_crypto_fee(ticker):
    cache_key = ("calculate_crypto_fee", ticker)
    if cache_key in requests_cache:
        return requests_cache[cache_key]
    url = f"https://api.cryptapi.io/{ticker}/estimate/"
    query = {
        "addresses": "2",
        "priority": "fast"
    }
    response = requests.get(url, params=query)
    data = response.json()
    requests_cache[cache_key] = data["estimated_cost"]
    return data["estimated_cost"]

def is_admin(user_id):
    if user_id in ADMIN_USER_IDS:
        return True
    return False

def add_crypto_fee(crypto_amount, crypto_fee_amount):
    result = Decimal(crypto_amount) + Decimal(add_to_fee_amount) * Decimal(crypto_fee_amount)
    return result.quantize(Decimal('1.0000000000')).normalize()

def transaction_received_send_to_final(context: CallbackContext, order_id: str):
    payment_type = get_order_payment_type(order_id)  # Retrieve payment type from order data
    username = get_order_username(order_id)
    user_id = get_order_user_id(order_id)
    store_name = get_store_name(order_id)
    final_order_group_chat_id = FINAL_ORDER_GROUP_CHAT_IDS.get(store_name)

    if final_order_group_chat_id:
        order_details_message = get_order_details_message(order_id, username, user_id)
        if payment_type == 'upfront':
            final_order_message = f"**Payment Confirmed for Order (Upfront Payment):**\n\n{order_details_message}"
            keyboard = [
                [InlineKeyboardButton("Ready", callback_data=f'check-ready_{order_id}')]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
        elif payment_type == 'on_confirmation':
            final_order_message = f"{PAYMENT_RECEIVED_CONFIRMATION}\n\n{order_details_message}"
            reply_markup=None
        else:
            final_order_message = f"**Payment Confirmed for Order:**\n\n{order_details_message}"
            keyboard = [
                [InlineKeyboardButton("Ready", callback_data=f'check-ready_{order_id}')]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
        if reply_markup:
            context.bot.send_message(chat_id=final_order_group_chat_id, text=final_order_message, parse_mode='Markdown', reply_markup=reply_markup)
        else:
            context.bot.send_message(chat_id=final_order_group_chat_id, text=final_order_message, parse_mode='Markdown')
        logging.info(f"Payment confirmation sent to final order group for order {order_id} with payment type {payment_type}")
    else:
        logging.error(f"Final order group chat ID not found for store {store_name}")

def monitor_transaction(context: CallbackContext):
    job = context.job
    order_id = job.context['order_id']
    user_id = job.context['user_id']
    start_time = job.context['start_time']
    elapsed_time = datetime.now() - start_time

    status = check_transaction_status(order_id)

    if status == 0:  # Confirmed
        amount_needed = get_order_amount_needed(order_id)
        currency = get_order_currency(order_id)
        amount_paid = get_order_amount_paid_fiat(order_id)
        amount_paid_dict = json.loads(amount_paid)
        amount_paid_ticker = float(amount_paid_dict[currency])

        if amount_paid_ticker < amount_needed:
            try:
                amount_still_needed = amount_needed - amount_paid_ticker
                crypto_coin = get_payment_coin(order_id).lower()

                crypto_amount = calculate_crypto_amount(amount_still_needed, currency, crypto_coin)
                crypto_fee_amount = calculate_crypto_fee(crypto_coin)

                total_amount = add_crypto_fee(crypto_amount, crypto_fee_amount)

                payment_address = create_payment_request(crypto_coin, user_id, order_id)

                payment_message = PAYMENT_REQUEST_REMAINING.format(
                    ticker=crypto_coin.upper(),
                    payment_address=payment_address,
                    total_amount=total_amount,
                    amount=amount_still_needed,
                    currency=currency
                )

                keyboard = [
                    [InlineKeyboardButton("Confirm transaction", callback_data=f'confirm-transaction_{order_id}')]
                ]
                reply_markup = InlineKeyboardMarkup(keyboard)
                sent_message = context.bot.send_message(chat_id=user_id, text=payment_message, reply_markup=reply_markup, parse_mode='Markdown')
                context.bot_data.setdefault('owing_messages', {}).setdefault(order_id, []).append(sent_message.message_id)
            except telegram.error.NetworkError as e:
                logging.error(f"Network error while sending payment request: {e}. Retrying next job run.")
            except Exception as e:
                logging.error(f"Unexpected error while processing payment request: {e}")
                job.schedule_removal()
                raise
            finally:
                job.schedule_removal()  # Ensure job is removed even if an error occurs
        else:
            try:
                sent_confirmed_message = context.bot.send_message(chat_id=user_id, text=TRANSACTION_CONFIRMED)
                context.bot_data.setdefault('confirmed_messages', {})[order_id] = sent_confirmed_message.message_id

                clear_cached_payments(user_id=user_id)

                monitoring_msg_id = context.bot_data.get('monitoring_messages', {}).get(order_id)
                if monitoring_msg_id:
                    try:
                        context.bot.delete_message(chat_id=user_id, message_id=monitoring_msg_id)
                    except BadRequest:
                        pass
                    context.bot_data['monitoring_messages'].pop(order_id, None)

                confirm_order_msg_id = context.bot_data.get('final_confirm_order_message', {}).get(order_id, [None])[0]
                if confirm_order_msg_id:
                    store_name = get_store_name(order_id)
                    final_order_group_chat_id = FINAL_ORDER_GROUP_CHAT_IDS.get(store_name)
                    try:
                        context.bot.delete_message(chat_id=final_order_group_chat_id, message_id=confirm_order_msg_id)
                    except BadRequest:
                        pass
                    context.bot_data['final_confirm_order_message'].pop(order_id, None)

                time.sleep(3)  # Wait before deleting additional messages

                crypto_information_msg_id = context.bot_data.get('crypto_transaction', {}).get(order_id)
                if crypto_information_msg_id:
                    try:
                        context.bot.delete_message(chat_id=user_id, message_id=crypto_information_msg_id)
                    except BadRequest:
                        pass
                    context.bot_data['crypto_transaction'].pop(order_id, None)

                confirmed_msg_id = context.bot_data.get('confirmed_messages', {}).get(order_id)
                if confirmed_msg_id:
                    try:
                        context.bot.delete_message(chat_id=user_id, message_id=confirmed_msg_id)
                    except BadRequest:
                        pass
                    context.bot_data['confirmed_messages'].pop(order_id, None)

                owing_message_ids = context.bot_data.get('owing_messages', {}).get(order_id, [])
                for msg_id in owing_message_ids:
                    try:
                        context.bot.delete_message(chat_id=user_id, message_id=msg_id)
                    except BadRequest:
                        pass
                context.bot_data.get('owing_messages', {}).pop(order_id, None)

                transaction_received_send_to_final(context, order_id)

                order_type = get_order_payment_type(order_id)
                if order_type == 'upfront':
                    user_id = get_order_user_id(order_id)
                    message = ORDER_UPFRONT_PAID.format(order_id=order_id)
                    edit_status(order_id, 'upfront_paid')
                    context.bot.send_message(chat_id=user_id, text=message, parse_mode='Markdown')
                else:  # 'on_confirmation'
                    user_id = get_order_user_id(order_id)
                    message = ORDER_CONFIRMING_PAID.format(order_id=order_id)
                    edit_status(order_id, 'done_on_confirmation')
                    context.bot.send_message(chat_id=user_id, text=message, parse_mode='Markdown')

                confirmation_message_id = context.bot_data.get('confirmation_messages', {}).get(order_id)
                request_group_chat_id = REQUEST_GROUP_CHAT_IDS.get(get_store_name(order_id))
                if confirmation_message_id and request_group_chat_id:
                    try:
                        context.bot.delete_message(chat_id=request_group_chat_id, message_id=confirmation_message_id)
                        logging.info(f"Deleted confirmation message for order {order_id} from request group.")
                    except BadRequest as e:
                        logging.error(f"Failed to delete confirmation message for order {order_id}: {e}")
                    context.bot_data['confirmation_messages'].pop(order_id, None)

                job.schedule_removal()  # Remove job after all operations are successful
            except telegram.error.NetworkError as e:
                logging.error(f"Network error: {e}. Retrying next job run.")
            except Exception as e:
                logging.error(f"Unexpected error: {e}")
                job.schedule_removal()
                raise

    elif elapsed_time >= timedelta(minutes=payment_timeout):
        try:
            context.bot.send_message(chat_id=user_id, text=TRANSACTION_TIMEOUT)
            job.schedule_removal()
        except telegram.error.NetworkError as e:
            logging.error(f"Network error while sending timeout message: {e}. Retrying next job run.")
        except Exception as e:
            logging.error(f"Unexpected error while sending timeout message: {e}")
            job.schedule_removal()
            raise

def escape_markdown(text):
    escape_chars = r'\_*[]()~`>#+-=|{}.!'
    return ''.join(['\\' + char if char in escape_chars else char for char in text]).replace('**', '\\*')

def purify_markdown(text):
    """Escapes MarkdownV2 special characters in a string."""
    escape_chars = r'\*_[]()~`>#+-=|{}.!'
    return ''.join(['\\' + char if char in escape_chars else char for char in text])

def ask_next_info(update: Update, context: CallbackContext) -> None:
    required_info = context.user_data.get('required_info')
    if required_info:
        next_info = required_info.pop(0)
        context.user_data['current_info'] = next_info
        if update.callback_query:
            if 'last_message' in context.user_data:
                try:
                    context.user_data['last_message'].delete()
                except BadRequest:
                    pass
            message = update.callback_query.message.reply_text(f"{NEXT_INFORMATION_PROMPT}\n{next_info}")
            context.user_data['last_message'] = message
        else:
            if 'last_message' in context.user_data:
                try:
                    context.user_data['last_message'].delete()
                except BadRequest:
                    pass
            message = update.message.reply_text(f"{NEXT_INFORMATION_PROMPT}\n{next_info}")
            context.user_data['last_message'] = message
    else:
        if update.callback_query:
            update.callback_query.message.reply_text("Thank you for providing all the required information.")
        else:
            update.message.reply_text("Thank you for providing all the required information.")

def show_overview(update: Update, context: CallbackContext) -> None:
    info_responses = context.user_data.get('info_responses', {})
    overview = "\n".join([f"{key}: {value}" for key, value in info_responses.items()])
    keyboard = [
        [InlineKeyboardButton("Confirm", callback_data='final_confirm')],
        [InlineKeyboardButton("Start over", callback_data='start_over')],
        [InlineKeyboardButton("Cancel Order", callback_data='cancel_order')]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)
    update.message.reply_text(f"{OVERVIEW_MESSAGE_TEXT}\n{overview}", reply_markup=reply_markup)

# Combine all group chat IDs into a blacklist
GROUP_CHAT_IDS_BLACKLIST = set(REQUEST_GROUP_CHAT_IDS.values()).union(FINAL_ORDER_GROUP_CHAT_IDS.values())

def handle_message(update: Update, context: CallbackContext) -> None:
    chat_id = update.effective_chat.id
    message = update.message

    if str(chat_id) in GROUP_CHAT_IDS_BLACKLIST:
        pay_upfront_message_id = context.chat_data.get('pay_upfront_message_id')
        if message.reply_to_message and message.reply_to_message.message_id == pay_upfront_message_id:
            if amount_currency_pattern.match(message.text.replace(" ", "")):
                # Extract amount and currency
                amount_currency = message.text.replace(" ", "")
                amount = re.findall(r'\d+', amount_currency)[0]
                currency = re.findall(r'[a-zA-Z]+', amount_currency)[0].upper()
                order_id = context.chat_data["pay_upfront_order_id"]
                # Send confirmation buttons to admin
                keyboard = [
                    [InlineKeyboardButton("Confirm amount", callback_data=f'confirm-pay-amount_{amount}_{currency}_{order_id}')],
                    [InlineKeyboardButton("Change amount", callback_data=f'change-pay-amount_{order_id}')]
                ]
                reply_markup = InlineKeyboardMarkup(keyboard)
                message_id = context.chat_data.get('order_approve_decline_messages').get(order_id)
                confirmation_message = context.bot.send_message(
                    chat_id=chat_id,
                    text=f"{CONFIRM_PAYMENT_AMOUNT_MESSAGE_TEXT}\n{amount} {currency}",
                    reply_markup=reply_markup,
                    reply_to_message_id=message_id[0]
                )

                context.bot.delete_message(chat_id=chat_id, message_id=message.message_id)

                # Store message IDs for cleanup
                context.chat_data.setdefault('order_messages', {}).setdefault(order_id, []).append(confirmation_message.message_id)
                # Store the admin's amount message ID for later deletion
                context.chat_data['admin_amount_message_id'] = message.message_id
                # Delete old messages
                try:
                    context.bot.delete_message(chat_id=chat_id, message_id=pay_upfront_message_id)
                    context.bot.delete_message(chat_id=chat_id, message_id=message.reply_to_message.message_id)
                    context.bot.delete_message(chat_id=chat_id, message_id=message.message_id)
                    if 'invalid_format_message_id' in context.chat_data:
                        context.bot.delete_message(chat_id=chat_id, message_id=context.chat_data['invalid_format_message_id'])
                        del context.chat_data['invalid_format_message_id']
                except BadRequest:
                    pass
            else:
                # Send invalid format message
                order_id = context.chat_data["pay_upfront_order_id"]
                message_id = context.chat_data.get('order_approve_decline_messages').get(order_id)
                invalid_message = context.bot.send_message(
                    chat_id=chat_id,
                    text=INVALID_AMOUNT_FORMAT,
                    reply_to_message_id=message_id[0]
                )
                context.chat_data['invalid_format_message_id'] = invalid_message.message_id
                # Delete user's invalid message and previous prompt
                try:
                    context.bot.delete_message(chat_id=chat_id, message_id=message.message_id)
                    context.bot.delete_message(chat_id=chat_id, message_id=message.reply_to_message.message_id)
                except BadRequest:
                    pass
                # Update pay_upfront_message_id to point to the new prompt
                context.chat_data['pay_upfront_message_id'] = invalid_message.message_id
        # Handle replies in the final order group
        final_order_pay_message_id = context.chat_data.get('final_order_pay_message_id')
        if message.reply_to_message and message.reply_to_message.message_id == final_order_pay_message_id:
            if amount_currency_pattern.match(message.text.replace(" ", "")):
                amount_currency = message.text.replace(" ", "")
                amount = re.findall(r'\d+', amount_currency)[0]
                currency = re.findall(r'[a-zA-Z]+', amount_currency)[0].upper()
                order_id = context.chat_data["final_order_pay_order_id"]
                # Send confirmation buttons
                keyboard = [
                    [InlineKeyboardButton("Confirm amount", callback_data=f'final-confirm-pay-amount_{amount}_{currency}_{order_id}')],
                    [InlineKeyboardButton("Change amount", callback_data=f'final-change-pay-amount_{order_id}')]
                ]
                reply_markup = InlineKeyboardMarkup(keyboard)

                reply_message_id = context.chat_data.get('final_order_info_message_id').get(order_id)[0]
                confirmation_message = context.bot.send_message(
                    chat_id=chat_id,
                    text=f"{CONFIRM_PAYMENT_AMOUNT_MESSAGE_TEXT}\n{amount} {currency}",
                    reply_markup=reply_markup,
                    reply_to_message_id=reply_message_id
                )

                context.bot.delete_message(chat_id=chat_id, message_id=message.message_id)

                # Store message IDs
                context.chat_data.setdefault('final_order_messages', {}).setdefault(order_id, []).append(confirmation_message.message_id)
                # Store the admin's amount message ID for later deletion
                context.chat_data['final_admin_amount_message_id'] = message.message_id
                # Delete old messages
                try:
                    context.bot.delete_message(chat_id=chat_id, message_id=final_order_pay_message_id)
                    context.bot.delete_message(chat_id=chat_id, message_id=message.reply_to_message.message_id)
                    context.bot.delete_message(chat_id=chat_id, message_id=message.message_id)
                    if 'final_invalid_format_message_id' in context.chat_data:
                        context.bot.delete_message(chat_id=chat_id, message_id=context.chat_data['final_invalid_format_message_id'])
                        del context.chat_data['final_invalid_format_message_id']
                except BadRequest:
                    pass

            else:
                # Send invalid format message
                order_id = context.chat_data["final_order_pay_order_id"]
                message_id = context.chat_data.get('order_approve_decline_messages').get(order_id)
                invalid_message = context.bot.send_message(
                    chat_id=chat_id,
                    text=INVALID_AMOUNT_FORMAT,
                    reply_to_message_id=message_id[0]
                )

                context.chat_data['final_invalid_format_message_id'] = invalid_message.message_id
                # Delete user's invalid message and previous prompt
                try:
                    context.bot.delete_message(chat_id=chat_id, message_id=message.message_id)
                    context.bot.delete_message(chat_id=chat_id, message_id=message.reply_to_message.message_id)
                except BadRequest:
                    pass
                # Update final_order_pay_message_id to point to the new prompt
                context.chat_data['final_order_pay_message_id'] = invalid_message.message_id
    else:
        sent_all_info = context.user_data.get('sent_all_info')  # Access from context
        if not sent_all_info:
            current_info = context.user_data.get('current_info')
            if current_info:
                if 'last_message' in context.user_data:
                    try:
                        context.user_data['last_message'].delete()
                    except:
                        pass
                context.user_data['info_responses'][current_info] = update.message.text
                if context.user_data['required_info']:
                    ask_next_info(update, context)
                else:
                    context.user_data['sent_all_info'] = True  # Update status
                    show_overview(update, context)

def ask_required_info(update: Update, context: CallbackContext, store_name: str) -> None:
    store = stores.get(store_name)
    if store:
        required_info = store['required_info'].split('\n')
        context.user_data['required_info'] = required_info
        context.user_data['info_responses'] = {}
        ask_next_info(update, context)

def generate_order_id(length=order_id_length):
    return ''.join(random.choices(string.ascii_uppercase + string.digits, k=length))

def get_order_details_message(order_id, username, user_id):
    order_details = get_order_details(order_id)
    store_name = get_store_name(order_id)  # Use a new function to get the store name
    return f"**📦 Order ID:** {order_id}\n**🏬 Store:** {store_name}\n**👤 Order by: @{username}**\n**👤 User ID: {user_id}**\n\n**📝 Order Details:**\n{order_details}"

def delete_chat_history(update: Update, context: CallbackContext) -> None:
    chat_id = update.effective_chat.id
    current_message_id = update.effective_message.message_id
    confirmation_message_id = context.user_data.get('confirmation_message_id')

    consecutive_failures = 0
    for message_id in range(current_message_id, 0, -1):
        if message_id == confirmation_message_id:
            continue
        try:
            context.bot.delete_message(chat_id=chat_id, message_id=message_id)
            consecutive_failures = 0
        except BadRequest:
            consecutive_failures += 1
            if consecutive_failures > 10:
                break

def delete_chat_commands(update: Update, context: CallbackContext) -> None:
    chat_id = update.effective_chat.id
    current_message_id = update.effective_message.message_id
    confirmation_message_id = context.user_data.get('confirmation_message_id')

    consecutive_failures = 0
    for message_id in range(current_message_id - 1, 0, -1):
        if message_id == confirmation_message_id:
            continue
        try:
            context.bot.delete_message(chat_id=chat_id, message_id=message_id)
            consecutive_failures = 0
        except BadRequest:
            consecutive_failures += 1
            if consecutive_failures > 5:
                break

def get_status_info(order_id):
    status_key = get_status(order_id)
    data = STATUS_MESSAGES.get(status_key, {})
    return data.get("message", ""), data.get("buttons", [])
